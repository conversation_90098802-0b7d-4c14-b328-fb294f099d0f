#!/usr/bin/env node

/**
 * 调试测试脚本 - 不依赖外部包
 */

console.log('🔧 调试测试开始...');
console.log('📍 当前工作目录:', process.cwd());
console.log('📍 Node.js 版本:', process.version);
console.log('📍 平台:', process.platform);

// 测试基本功能
console.log('\n🧪 测试基本功能...');

// 测试 crypto
try {
    const crypto = await import('crypto');
    const uuid = crypto.randomUUID();
    console.log('✅ crypto 模块正常, UUID:', uuid);
} catch (error) {
    console.error('❌ crypto 模块失败:', error.message);
}

// 测试 fs
try {
    const fs = await import('fs');
    console.log('✅ fs 模块正常');
} catch (error) {
    console.error('❌ fs 模块失败:', error.message);
}

// 测试 fetch
console.log('\n🌐 测试网络功能...');
try {
    let fetch;
    if (globalThis.fetch) {
        fetch = globalThis.fetch;
        console.log('✅ 使用内置 fetch');
    } else {
        console.log('📥 尝试导入 node-fetch...');
        const nodeFetch = await import('node-fetch');
        fetch = nodeFetch.default;
        console.log('✅ node-fetch 导入成功');
    }
    
    // 测试简单请求
    console.log('🔗 测试网络请求...');
    const response = await fetch('https://httpbin.org/get', {
        method: 'GET',
        headers: {
            'User-Agent': 'Debug-Test/1.0'
        }
    });
    
    if (response.ok) {
        console.log('✅ 网络请求成功, 状态:', response.status);
    } else {
        console.log('⚠️ 网络请求失败, 状态:', response.status);
    }
    
} catch (error) {
    console.error('❌ 网络测试失败:', error.message);
    if (error.message.includes('node-fetch')) {
        console.log('💡 提示: 请运行 npm install node-fetch');
    }
}

// 测试 ethers
console.log('\n🔐 测试 ethers 功能...');
try {
    console.log('📥 尝试导入 ethers...');
    const ethers = await import('ethers');
    console.log('✅ ethers 导入成功');
    
    // 测试创建钱包
    const testPrivateKey = '0x' + 'c'.repeat(64);
    const wallet = new ethers.Wallet(testPrivateKey);
    console.log('✅ 钱包创建成功, 地址:', wallet.address);
    
} catch (error) {
    console.error('❌ ethers 测试失败:', error.message);
    if (error.message.includes('ethers')) {
        console.log('💡 提示: 请运行 npm install ethers');
    }
}

// 测试 OKX API
console.log('\n🔗 测试 OKX API...');
try {
    let fetch;
    if (globalThis.fetch) {
        fetch = globalThis.fetch;
    } else {
        const nodeFetch = await import('node-fetch');
        fetch = nodeFetch.default;
    }
    
    const testUrl = 'https://web3.okx.com/amplitude/2/httpapi';
    const testData = {
        api_key: "56bf9d43d57f079e506b4f26c70a698f",
        events: [{
            user_id: "",
            device_id: "test-device-id",
            session_id: Date.now().toString(),
            time: Date.now(),
            event_type: "test_event",
            event_properties: {
                test: true
            }
        }]
    };
    
    console.log('📤 发送测试请求到 OKX API...');
    const response = await fetch(testUrl, {
        method: 'POST',
        headers: {
            'Accept': '*/*',
            'Content-Type': 'application/json',
            'User-Agent': 'Debug-Test/1.0'
        },
        body: JSON.stringify(testData)
    });
    
    console.log('📨 OKX API 响应状态:', response.status);
    
    if (response.ok) {
        console.log('✅ OKX API 连接正常');
    } else {
        console.log('⚠️ OKX API 响应异常');
    }
    
} catch (error) {
    console.error('❌ OKX API 测试失败:', error.message);
}

console.log('\n🎯 调试测试完成！');
console.log('如果所有测试都通过，说明环境正常');
console.log('如果有失败的测试，请根据提示安装相应的依赖包');
