# OKX 推特绑定 API 使用指南

## 🐦 概述
本文档基于 2025-08-06 的实际抓包数据，详细说明了 OKX 平台推特账号绑定的 API 接口使用方法。

## 🔗 核心API接口

### 1. 获取Twitter OAuth2授权URL
**接口**: `POST /priapi/v1/dapp/oauth2/oauth2-url`

**请求体**:
```json
{
  "domain": "web3.okx.com",
  "walletAddress": "0xYourWalletAddress",
  "platform": 1,
  "bizType": 1,
  "bizRequestData": {
    "giveawayId": 389,
    "chainId": 8453
  }
}
```

**响应**:
```json
{
  "code": 0,
  "data": {
    "clientId": "RVE1ZGwyREQyeGFhaVFwTTFhX0E6MTpjaQ",
    "url": "https://x.com/i/oauth2/authorize?response_type=code&client_id=RVE1ZGwyREQyeGFhaVFwTTFhX0E6MTpjaQ&redirect_uri=https%3A%2F%2Fweb3.okx.com%2Fmarketplace%2Fdrops%2Fshare%3Fplatform%3Dtwitter&scope=tweet.read+users.read+follows.read+offline.access&state=state&code_challenge=challenge&code_challenge_method=plain"
  }
}
```

### 2. 处理Twitter OAuth2回调
**接口**: `POST /priapi/v1/dapp/oauth2/call-back`

**请求体**:
```json
{
  "code": "TwitterAuthorizationCode",
  "clientId": "RVE1ZGwyREQyeGFhaVFwTTFhX0E6MTpjaQ",
  "walletAddress": "0xYourWalletAddress",
  "platform": 1,
  "bizType": 1,
  "bizRequestData": {
    "giveawayId": 389,
    "chainId": 8453
  },
  "domain": "web3.okx.com"
}
```

**响应**:
```json
{
  "code": 0,
  "data": {
    "bindStatus": 1,
    "bindedWalletAddress": ""
  }
}
```

## 🔐 必需的认证头部

### 基础头部
```
Accept: application/json
App-Type: web
Content-Type: application/json
Referer: https://web3.okx.com/zh-hans/giveaway/jaspervault
X-Cdn: https://web3.okx.com
X-Locale: zh_CN
X-Utc: 8
```

### 认证头部（动态生成）
```
Ok-Timestamp: [当前时间戳]
Ok-Verify-Sign: [签名]
Ok-Verify-Token: [令牌]
X-FpToken-Signature: [指纹签名]
X-Id-Group: [用户组ID]
X-Request-Timestamp: [请求时间戳]
Devid: [设备ID]
```

## 🔄 完整绑定流程

### 步骤1: 请求OAuth2授权URL
```javascript
const oauth2Response = await fetch('https://web3.okx.com/priapi/v1/dapp/oauth2/oauth2-url?t=' + Date.now(), {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Ok-Timestamp': timestamp,
    'Ok-Verify-Sign': signature,
    'Ok-Verify-Token': token,
    // ... 其他必需头部
  },
  body: JSON.stringify({
    domain: 'web3.okx.com',
    walletAddress: walletAddress,
    platform: 1,
    bizType: 1,
    bizRequestData: {
      giveawayId: 389,
      chainId: 8453
    }
  })
});
```

### 步骤2: 跳转到Twitter授权页面
```javascript
const { clientId, url } = oauth2Response.data;
// 跳转到 Twitter 授权页面
window.open(url, '_blank');
```

### 步骤3: 处理授权回调
```javascript
// Twitter 授权完成后，会重定向回 OKX 页面并带上授权码
const authCode = getAuthCodeFromUrl(); // 从URL参数中提取

const callbackResponse = await fetch('https://web3.okx.com/priapi/v1/dapp/oauth2/call-back?t=' + Date.now(), {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Ok-Timestamp': timestamp,
    'Ok-Verify-Sign': signature,
    'Ok-Verify-Token': token,
    // ... 其他必需头部
  },
  body: JSON.stringify({
    code: authCode,
    clientId: clientId,
    walletAddress: walletAddress,
    platform: 1,
    bizType: 1,
    bizRequestData: {
      giveawayId: 389,
      chainId: 8453
    },
    domain: 'web3.okx.com'
  })
});
```

## 📊 参数说明

### platform 参数
- `1`: Twitter/X 平台

### bizType 参数  
- `1`: Giveaway 业务类型

### bindStatus 返回值
- `1`: 绑定成功
- `0`: 绑定失败

### Twitter OAuth2 权限范围
- `tweet.read`: 读取推文
- `users.read`: 读取用户信息
- `follows.read`: 读取关注信息
- `offline.access`: 离线访问

## ⚠️ 注意事项

### 1. 认证签名
- 所有API调用都需要正确的签名和令牌
- 签名算法需要与OKX前端保持一致
- 时间戳需要与服务器时间同步

### 2. OAuth2流程
- 授权URL有时效性，需要及时使用
- 授权码只能使用一次
- 重定向URI必须与注册的一致

### 3. 错误处理
- 检查响应中的 `code` 字段，0表示成功
- `bindStatus` 为1表示绑定成功
- 网络错误需要重试机制

## 🔧 自动化实现

### 在脚本中集成
```javascript
class TwitterBinding {
  async bindTwitterAccount(walletAddress, giveawayId, chainId) {
    try {
      // 步骤1: 获取授权URL
      const oauth2Url = await this.getOAuth2Url(walletAddress, giveawayId, chainId);
      
      // 步骤2: 模拟用户授权（需要实际的Twitter登录）
      const authCode = await this.simulateTwitterAuth(oauth2Url.url);
      
      // 步骤3: 完成绑定
      const bindResult = await this.completeBinding(authCode, oauth2Url.clientId, walletAddress, giveawayId, chainId);
      
      return bindResult.bindStatus === 1;
    } catch (error) {
      console.error('Twitter绑定失败:', error);
      return false;
    }
  }
}
```

## 📝 示例完整请求

### 获取OAuth2 URL
```bash
curl -X POST 'https://web3.okx.com/priapi/v1/dapp/oauth2/oauth2-url?t=*************' \
  -H 'Content-Type: application/json' \
  -H 'Ok-Timestamp: *************' \
  -H 'Ok-Verify-Sign: uIJKlWTU6VTvRWs9vfmn5gNZDM4onqaqsT7C5GCSeLI=' \
  -H 'Ok-Verify-Token: b79777af-4da9-42fc-89f4-e15bbb649793' \
  -d '{
    "domain": "web3.okx.com",
    "walletAddress": "0xYourWalletAddress",
    "platform": 1,
    "bizType": 1,
    "bizRequestData": {
      "giveawayId": 389,
      "chainId": 8453
    }
  }'
```

### 处理回调
```bash
curl -X POST 'https://web3.okx.com/priapi/v1/dapp/oauth2/call-back?t=1754522118914' \
  -H 'Content-Type: application/json' \
  -H 'Ok-Timestamp: 1754522118914' \
  -H 'Ok-Verify-Sign: QiV4AVvKP8nw5LU8MEBT/0AnC7MWLorCNK1a4cbIoGE=' \
  -H 'Ok-Verify-Token: 940b7551-937f-4bf6-b35c-58f7235ee82b' \
  -d '{
    "code": "TwitterAuthCode",
    "clientId": "RVE1ZGwyREQyeGFhaVFwTTFhX0E6MTpjaQ",
    "walletAddress": "0xYourWalletAddress",
    "platform": 1,
    "bizType": 1,
    "bizRequestData": {
      "giveawayId": 389,
      "chainId": 8453
    },
    "domain": "web3.okx.com"
  }'
```

---
*数据捕获时间: 2025-08-06 23:15:00 UTC*
*总请求数: 10*
*绑定状态: 成功*
