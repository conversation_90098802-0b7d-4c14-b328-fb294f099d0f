// 推特 Tokens 自动获取脚本
// 基于推特自动登录.mjs，专门为 OKX Giveaway 脚本生成所需的 twitter_tokens.json

import fs from 'fs';
import path from 'path';

// 配置区域 - 需要填入真实的推特账号信息
const accounts = [
    {
        account: 'bdumdums',  // 邮箱或用户名或手机号
        screen_name: 'bdumdums',       // 用户名或手机号
        password: 'NguuVQy2Ed4sYt3',          // 密码
        _2fa: '',                          // TOTP 二次验证码（如果有）
        acid: ''                           // 邮箱验证码（如果需要）
    }
    // 可以添加更多账号
    // {
    //     account: '<EMAIL>',
    //     screen_name: 'another_username', 
    //     password: 'another_password',
    //     _2fa: '',
    //     acid: ''
    // }
];

// Twitter API 配置
const bearer_token = 'Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA';

// 发送登录请求
const sendLoginRequest = async (bearer_token, guest_token, cookie = {}, headers = {}, query = new URLSearchParams({}), body = {}) => {
    return fetch(`https://api.twitter.com/1.1/onboarding/task.json${query.size > 0 ? `?${query.toString()}` : ''}`, {
        method: 'POST',
        headers: {
            'content-type': 'application/json',
            authorization: bearer_token,
            'x-guest-token': guest_token,
            cookie: Object.entries(cookie)
                .map(([key, value]) => `${key}=${value}`)
                .join('; '),
            ...headers
        },
        body: JSON.stringify(body)
    })
        .then(async (response) => ({
            message: '',
            cookies: Object.fromEntries(
                [...response.headers.entries()]
                    .filter((header) => header[0] === 'set-cookie')
                    .map((header) => {
                        const tmpCookie = header[1].split(';')[0]
                        const firstEqual = tmpCookie.indexOf('=')
                        return [tmpCookie.slice(0, firstEqual), tmpCookie.slice(firstEqual + 1)]
                    })
            ),
            content: await response.json(),
            headers: response.headers
        }))
        .then((res) => {
            console.log(`📡 API 响应状态: ${res.content.flow_token ? '成功' : '失败'}`);
            return res
        })
        .catch((error) => {
            return {
                message: error.message,
                cookies: {},
                content: {},
                headers: new Map()
            }
        })
}

// 获取用户信息
const getViewer = async (bearer_token, cookie, viewerQueryID, viewerFeatures) =>
    fetch(
        `https://api.twitter.com/graphql/${viewerQueryID}/Viewer?` +
            new URLSearchParams({
                variables: JSON.stringify({ withCommunitiesMemberships: true, withSubscribedTab: true, withCommunitiesCreation: true }),
                features: JSON.stringify(viewerFeatures)
            }).toString(),
        {
            headers: {
                authorization: bearer_token,
                'x-csrf-token': cookie.ct0,
                cookie: Object.entries(cookie)
                    .map(([key, value]) => `${key}=${value}`)
                    .join('; ')
            }
        }
    )
        .then(async (response) => ({
            message: '',
            cookies: Object.fromEntries(
                [...response.headers.entries()]
                    .filter((header) => header[0] === 'set-cookie')
                    .map((header) => {
                        const tmpCookie = header[1].split(';')[0]
                        const firstEqual = tmpCookie.indexOf('=')
                        return [tmpCookie.slice(0, firstEqual), tmpCookie.slice(firstEqual + 1)]
                    })
            ),
            content: await response.json()
        }))
        .then((res) => {
            return res
        })
        .catch((error) => {
            return {
                message: error.message,
                cookies: {},
                content: {}
            }
        })

// 单个账号登录并获取 tokens
async function loginAndGetTokens(accountConfig) {
    console.log(`\n🔐 开始登录账号: ${accountConfig.account}`);
    
    try {
        // 获取 guest token
        console.log('📡 获取 guest token...');
        const guest_token = (
            await (
                await fetch('https://api.twitter.com/1.1/guest/activate.json', {
                    method: 'POST',
                    headers: {
                        authorization: bearer_token
                    }
                })
            ).json()
        ).guest_token;

        console.log(`✅ Guest token: ${guest_token.substring(0, 20)}...`);

        let cookie = {};
        let headers = {};
        let response = {};

        // 开始登录流程
        console.log('🚀 开始登录流程...');
        const login = await sendLoginRequest(
            bearer_token,
            guest_token,
            cookie,
            headers,
            new URLSearchParams({
                flow_name: 'login'
            }),
            {
                input_flow_data: { 
                    flow_context: { 
                        debug_overrides: {}, 
                        start_location: { location: 'unknown' } 
                    } 
                },
                subtask_versions: {
                    action_list: 2,
                    alert_dialog: 1,
                    app_download_cta: 1,
                    check_logged_in_account: 1,
                    choice_selection: 3,
                    contacts_live_sync_permission_prompt: 0,
                    cta: 7,
                    email_verification: 2,
                    end_flow: 1,
                    enter_date: 1,
                    enter_email: 2,
                    enter_password: 5,
                    enter_phone: 2,
                    enter_recaptcha: 1,
                    enter_text: 5,
                    enter_username: 2,
                    generic_urt: 3,
                    in_app_notification: 1,
                    interest_picker: 3,
                    js_instrumentation: 1,
                    menu_dialog: 1,
                    notifications_permission_prompt: 2,
                    open_account: 2,
                    open_home_timeline: 1,
                    open_link: 1,
                    phone_verification: 4,
                    privacy_options: 1,
                    security_key: 3,
                    select_avatar: 4,
                    select_banner: 2,
                    settings_list: 7,
                    show_code: 1,
                    sign_up: 2,
                    sign_up_review: 4,
                    tweet_selection_urt: 1,
                    update_users: 1,
                    upload_media: 1,
                    user_recommendations_list: 4,
                    user_recommendations_urt: 1,
                    wait_spinner: 3,
                    web_modal: 1
                }
            }
        );

        cookie = { ...cookie, ...login.cookies };
        response = login.content;

        // JS Instrumentation (网页版需要)
        console.log('🔧 执行 JS Instrumentation...');
        const LoginJsInstrumentationSubtask = await sendLoginRequest(bearer_token, guest_token, cookie, headers, new URLSearchParams({}), {
            flow_token: response.flow_token,
            subtask_inputs: [
                {
                    js_instrumentation: {
                        link: 'next_link',
                        response: '{}'
                    },
                    subtask_id: 'LoginJsInstrumentationSubtask'
                }
            ]
        });

        cookie = { ...cookie, ...LoginJsInstrumentationSubtask.cookies };
        response = LoginJsInstrumentationSubtask.content;

        // 输入用户标识符
        console.log('👤 输入用户标识符...');
        const LoginEnterUserIdentifierSSO = await sendLoginRequest(bearer_token, guest_token, cookie, headers, new URLSearchParams({}), {
            flow_token: response.flow_token,
            subtask_inputs: [
                {
                    settings_list: {
                        link: 'next_link',
                        setting_responses: [
                            {
                                key: 'user_identifier',
                                response_data: {
                                    text_data: {
                                        result: accountConfig.account
                                    }
                                }
                            }
                        ]
                    },
                    subtask_id: 'LoginEnterUserIdentifierSSO'
                }
            ]
        });

        cookie = { ...cookie, ...LoginEnterUserIdentifierSSO.cookies };
        response = LoginEnterUserIdentifierSSO.content;

        // 处理备用标识符（如果需要）
        if (LoginEnterUserIdentifierSSO.content.subtasks[0]?.subtask_id === 'LoginEnterAlternateIdentifierSubtask') {
            console.log('🔄 输入备用标识符...');
            const LoginEnterAlternateIdentifierSubtask = await sendLoginRequest(bearer_token, guest_token, cookie, headers, new URLSearchParams({}), {
                flow_token: response.flow_token,
                subtask_inputs: [
                    {
                        enter_text: {
                            link: 'next_link',
                            text: accountConfig.screen_name
                        },
                        subtask_id: 'LoginEnterAlternateIdentifierSubtask'
                    }
                ]
            });

            cookie = { ...cookie, ...LoginEnterAlternateIdentifierSubtask.cookies };
            response = LoginEnterAlternateIdentifierSubtask.content;
        }

        // 输入密码
        console.log('🔑 输入密码...');
        const LoginEnterPassword = await sendLoginRequest(bearer_token, guest_token, cookie, headers, new URLSearchParams({}), {
            flow_token: response.flow_token,
            subtask_inputs: [
                {
                    enter_password: {
                        link: 'next_link',
                        password: accountConfig.password
                    },
                    subtask_id: 'LoginEnterPassword'
                }
            ]
        });

        cookie = { ...cookie, ...LoginEnterPassword.cookies };
        response = LoginEnterPassword.content;

        // 账号重复检查
        console.log('🔍 执行账号重复检查...');
        const AccountDuplicationCheck = await sendLoginRequest(bearer_token, guest_token, cookie, headers, new URLSearchParams({}), {
            flow_token: response.flow_token,
            subtask_inputs: [
                {
                    check_logged_in_account: {
                        link: 'AccountDuplicationCheck_false'
                    },
                    subtask_id: 'AccountDuplicationCheck'
                }
            ]
        });

        cookie = { ...cookie, ...AccountDuplicationCheck.cookies };
        response = AccountDuplicationCheck.content;

        // 处理二次验证（如果需要）
        if (AccountDuplicationCheck.content.subtasks[0]?.subtask_id === 'LoginTwoFactorAuthChallenge') {
            console.log('🔐 处理二次验证...');

            // 选择验证方法（如果需要）
            if (!AccountDuplicationCheck.content.subtasks[0]?.enter_text) {
                const LoginTwoFactorAuthChooseMethod = await sendLoginRequest(bearer_token, guest_token, cookie, headers, new URLSearchParams({}), {
                    flow_token: response.flow_token,
                    subtask_inputs: [
                        {
                            choice_selection: {
                                link: 'next_link',
                                selected_choices: ['0']
                            },
                            subtask_id: 'LoginTwoFactorAuthChooseMethod'
                        }
                    ]
                });

                cookie = { ...cookie, ...LoginTwoFactorAuthChooseMethod.cookies };
                response = LoginTwoFactorAuthChooseMethod.content;
            }

            // 输入二次验证码
            const LoginTwoFactorAuthChallenge = await sendLoginRequest(bearer_token, guest_token, cookie, headers, new URLSearchParams({}), {
                flow_token: response.flow_token,
                subtask_inputs: [
                    {
                        enter_text: {
                            link: 'next_link',
                            text: accountConfig._2fa
                        },
                        subtask_id: 'LoginTwoFactorAuthChallenge'
                    }
                ]
            });

            cookie = { ...cookie, ...LoginTwoFactorAuthChallenge.cookies };
            response = LoginTwoFactorAuthChallenge.content;
        }

        // 处理邮箱验证（如果需要）
        if (AccountDuplicationCheck.content.subtasks[0]?.subtask_id === 'LoginAcid') {
            console.log('📧 处理邮箱验证...');
            const LoginAcid = await sendLoginRequest(bearer_token, guest_token, cookie, headers, new URLSearchParams({}), {
                flow_token: response.flow_token,
                subtask_inputs: [
                    {
                        enter_text: {
                            text: accountConfig.acid,
                            link: 'next_link'
                        },
                        subtask_id: 'LoginAcid'
                    }
                ]
            });

            cookie = { ...cookie, ...LoginAcid.cookies };
            response = LoginAcid.content;
        }

        // 获取用户信息和最终的 cookies
        console.log('👤 获取用户信息...');
        const viewer = await getViewer(bearer_token, cookie, 'qevmDaYaF66EOtboiNoQbQ', {
            responsive_web_graphql_exclude_directive_enabled: true,
            verified_phone_label_enabled: false,
            creator_subscriptions_tweet_preview_api_enabled: true,
            responsive_web_graphql_skip_user_profile_image_extensions_enabled: false,
            responsive_web_graphql_timeline_navigation_enabled: true
        });

        cookie = { ...cookie, ...viewer.cookies };

        // 提取关键的认证信息
        const tokens = {
            timestamp: new Date().toISOString(),
            account: accountConfig.account,
            screen_name: viewer.content?.data?.viewer?.legacy?.screen_name || accountConfig.screen_name,
            user_id: viewer.content?.data?.viewer?.rest_id,
            tokens: {
                cookies: {
                    auth_token: cookie.auth_token,
                    ct0: cookie.ct0,
                    twid: cookie.twid,
                    sess: cookie.sess,
                    kdt: cookie.kdt,
                    remember_checked_on: cookie.remember_checked_on,
                    guest_id: cookie.guest_id
                },
                localStorage: {
                    // 可以添加其他需要的 localStorage 数据
                }
            }
        };

        console.log(`✅ 成功获取 ${accountConfig.account} 的 tokens`);
        console.log(`👤 用户: @${tokens.screen_name} (ID: ${tokens.user_id})`);

        return tokens;

    } catch (error) {
        console.error(`❌ 登录失败 ${accountConfig.account}:`, error.message);
        return null;
    }
}

// 批量获取所有账号的 tokens
async function getAllTokens() {
    console.log('🎯 开始批量获取 Twitter Tokens');
    console.log(`📊 总共 ${accounts.length} 个账号需要处理\n`);

    const allTokens = [];
    const results = {
        success: 0,
        failed: 0,
        errors: []
    };

    for (let i = 0; i < accounts.length; i++) {
        const account = accounts[i];
        console.log(`\n📋 进度: ${i + 1}/${accounts.length}`);
        console.log(`🔐 处理账号: ${account.account}`);

        try {
            const tokens = await loginAndGetTokens(account);

            if (tokens) {
                allTokens.push(tokens);
                results.success++;
                console.log(`✅ 账号 ${i + 1} 处理成功`);
            } else {
                results.failed++;
                results.errors.push(`账号 ${i + 1} (${account.account}) 登录失败`);
                console.log(`❌ 账号 ${i + 1} 处理失败`);
            }

        } catch (error) {
            results.failed++;
            results.errors.push(`账号 ${i + 1} (${account.account}) 异常: ${error.message}`);
            console.error(`❌ 账号 ${i + 1} 处理异常:`, error.message);
        }

        // 避免请求过于频繁
        if (i < accounts.length - 1) {
            console.log('⏸️ 等待 5 秒后处理下一个账号...');
            await new Promise(resolve => setTimeout(resolve, 5000));
        }
    }

    return { allTokens, results };
}

// 保存 tokens 到文件
function saveTokensToFile(tokens, filename = 'twitter_tokens.json') {
    try {
        if (tokens.length === 1) {
            // 单个账号，保存为单个 JSON 对象
            fs.writeFileSync(filename, JSON.stringify(tokens[0], null, 2));
        } else {
            // 多个账号，保存为 JSON 数组
            fs.writeFileSync(filename, JSON.stringify(tokens, null, 2));
        }

        console.log(`💾 Tokens 已保存到: ${filename}`);
        return true;
    } catch (error) {
        console.error(`❌ 保存文件失败: ${error.message}`);
        return false;
    }
}

// 验证配置
function validateConfig() {
    if (accounts.length === 0) {
        console.error('❌ 未配置任何账号信息');
        console.log('📝 请在脚本顶部的 accounts 数组中添加账号信息');
        return false;
    }

    for (let i = 0; i < accounts.length; i++) {
        const account = accounts[i];
        if (!account.account || !account.password) {
            console.error(`❌ 账号 ${i + 1} 缺少必要信息 (account 或 password)`);
            return false;
        }
    }

    return true;
}

// 主函数
async function main() {
    console.log('🎯 Twitter Tokens 自动获取脚本');
    console.log('=' .repeat(50));

    // 验证配置
    if (!validateConfig()) {
        process.exit(1);
    }

    try {
        // 获取所有 tokens
        const { allTokens, results } = await getAllTokens();

        // 显示结果摘要
        console.log('\n📊 执行结果摘要:');
        console.log('=' .repeat(50));
        console.log(`✅ 成功: ${results.success} 个账号`);
        console.log(`❌ 失败: ${results.failed} 个账号`);
        console.log(`📈 成功率: ${(results.success / accounts.length * 100).toFixed(1)}%`);

        if (results.errors.length > 0) {
            console.log('\n❌ 错误详情:');
            results.errors.forEach(error => console.log(`   - ${error}`));
        }

        // 保存成功的 tokens
        if (allTokens.length > 0) {
            console.log(`\n💾 保存 ${allTokens.length} 个有效 tokens...`);

            // 保存主文件
            saveTokensToFile(allTokens, 'twitter_tokens.json');

            // 同时保存备份文件
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            saveTokensToFile(allTokens, `twitter_tokens_backup_${timestamp}.json`);

            console.log('\n🎉 Twitter Tokens 获取完成！');
            console.log('📁 文件已生成:');
            console.log('   - twitter_tokens.json (主文件)');
            console.log(`   - twitter_tokens_backup_${timestamp}.json (备份文件)`);

        } else {
            console.log('\n❌ 未获取到任何有效的 tokens');
        }

    } catch (error) {
        console.error('❌ 脚本执行失败:', error.message);
        process.exit(1);
    }
}

// 运行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}
