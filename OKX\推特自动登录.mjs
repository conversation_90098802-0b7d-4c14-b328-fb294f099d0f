// node.js v18+ / deno / bun...

// config

const account = 'bdumdums' // email or screen_name or phone number
const screen_name = 'bdumdums' // screen_name or phone number
const password = 'NguuVQy2Ed4sYt3' // password
const _2fa = '' // TOTP
const acid = '' // Email code

const android_id = '' // or just keep empty? // Android id is a 64-bit number (as a hex string), everyone can get one from fcm

// true: `oauth_token` & `oauth_token_secret`
// false: get `auth_token` & `ct0`
const isAndroid = false //android_id !== ''

// init

let bearer_token = 'Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA'
if (isAndroid) {
    bearer_token = 'Bearer AAAAAAAAAAAAAAAAAAAAAFXzAwAAAAAAMHCxpeSDG1gLNLghVe8d74hl6k4%****************************************************'
}

const guest_token = (
    await (
        await fetch('https://api.twitter.com/1.1/guest/activate.json', {
            method: 'POST',
            headers: {
                authorization: bearer_token
            }
        })
    ).json()
).guest_token

// web requests
const sendLoginRequest = async (bearer_token, guest_token, cookie = {}, headers = {}, query = new URLSearchParams({}), body = {}) => {
    return fetch(`https://api.twitter.com/1.1/onboarding/task.json${query.size > 0 ? `?${query.toString()}` : ''}`, {
        method: 'POST',
        headers: {
            'content-type': 'application/json',
            authorization: bearer_token,
            'x-guest-token': guest_token,
            cookie: Object.entries(cookie)
                .map(([key, value]) => `${key}=${value}`)
                .join('; '),
            ...headers
        },
        body: JSON.stringify(body)
    })
        .then(async (response) => ({
            message: '',
            cookies: Object.fromEntries(
                [...response.headers.entries()]
                    .filter((header) => header[0] === 'set-cookie')
                    .map((header) => {
                        const tmpCookie = header[1].split(';')[0]
                        const firstEqual = tmpCookie.indexOf('=')
                        return [tmpCookie.slice(0, firstEqual), tmpCookie.slice(firstEqual + 1)]
                    })
            ),
            content: await response.json(),
            headers: response.headers
        }))
        .then((res) => {
            console.log(JSON.stringify(res, null, 4))
            return res
        })
        .catch((error) => {
            //console.error(error)
            return {
                message: error.message,
                cookies: {},
                content: {},
                headers: new Map()
            }
        })
}

const getViewer = async (bearer_token, cookie, viewerQueryID, viewerFeatures) =>
    fetch(
        `https://api.twitter.com/graphql/${viewerQueryID}/Viewer?` +
            new URLSearchParams({
                variables: JSON.stringify({ withCommunitiesMemberships: true, withSubscribedTab: true, withCommunitiesCreation: true }),
                features: JSON.stringify(viewerFeatures)
            }).toString(),
        {
            headers: {
                authorization: bearer_token,
                'x-csrf-token': cookie.ct0,
                cookie: Object.entries(cookie)
                    .map(([key, value]) => `${key}=${value}`)
                    .join('; ')
            }
        }
    )
        .then(async (response) => ({
            message: '',
            cookies: Object.fromEntries(
                [...response.headers.entries()]
                    .filter((header) => header[0] === 'set-cookie')
                    .map((header) => {
                        const tmpCookie = header[1].split(';')[0]
                        const firstEqual = tmpCookie.indexOf('=')
                        return [tmpCookie.slice(0, firstEqual), tmpCookie.slice(firstEqual + 1)]
                    })
            ),
            content: await response.json()
        }))
        .then((res) => {
            //console.log(res)
            return res
        })
        .catch((error) => {
            //console.error(error)
            return {
                message: error.message,
                cookies: {},
                content: {}
            }
        })

let cookie = {}
let headers = {}

if (isAndroid) {
    headers = {
        'User-Agent': 'TwitterAndroid/10.21.0-release.0',
        'X-Twitter-API-Version': 5,
        'X-Twitter-Client': 'TwitterAndroid',
        'X-Twitter-Client-Version': '10.21.0-release.0',
        'X-Twitter-Active-User': 'yes',
        'X-Twitter-Client-DeviceID': android_id
    }
}

let response = {}

// login

const login = await sendLoginRequest(
    bearer_token,
    guest_token,
    cookie,
    headers,
    new URLSearchParams({
        flow_name: 'login',
        ...(isAndroid
            ? {
                  api_version: '1',
                  known_device_token: '',
                  sim_country_code: 'us'
              }
            : {})
    }),
    {
        input_flow_data: !isAndroid
            ? { flow_context: { debug_overrides: {}, start_location: { location: 'unknown' } } }
            : {
                  country_code: null,
                  flow_context: { referrer_context: { referral_details: 'utm_source=google-play&utm_medium=organic', referrer_url: '' }, start_location: { location: 'deeplink' } },
                  requested_variant: null,
                  target_user_id: 0
              },
        subtask_versions: {
            action_list: 2,
            alert_dialog: 1,
            app_download_cta: 1,
            check_logged_in_account: 1,
            choice_selection: 3,
            contacts_live_sync_permission_prompt: 0,
            cta: 7,
            email_verification: 2,
            end_flow: 1,
            enter_date: 1,
            enter_email: 2,
            enter_password: 5,
            enter_phone: 2,
            enter_recaptcha: 1,
            enter_text: 5,
            enter_username: 2,
            generic_urt: 3,
            in_app_notification: 1,
            interest_picker: 3,
            js_instrumentation: 1,
            menu_dialog: 1,
            notifications_permission_prompt: 2,
            open_account: 2,
            open_home_timeline: 1,
            open_link: 1,
            phone_verification: 4,
            privacy_options: 1,
            security_key: 3,
            select_avatar: 4,
            select_banner: 2,
            settings_list: 7,
            show_code: 1,
            sign_up: 2,
            sign_up_review: 4,
            tweet_selection_urt: 1,
            update_users: 1,
            upload_media: 1,
            user_recommendations_list: 4,
            user_recommendations_urt: 1,
            wait_spinner: 3,
            web_modal: 1
        }
    }
)

// ATT!!! // required// for android
if (!headers.att && login.headers.has('att')) {
    headers.att = login.headers.get('att') || ''
}

cookie = { ...cookie, ...login.cookies }
response = login.content

if (!isAndroid) {
    // LoginJsInstrumentationSubtask

    // NOT NECESSARY
    // const JsInstCookie = fetch('https://twitter.com/i/js_inst?c_name=ui_metrics', {
    //     headers: {
    //         cookie: Object.entries(cookie).map(([key, value]) => `${key}=${value}`).join('; ')
    //     }
    // }).then(response => Object.fromEntries([...response.headers.entries()].filter((header) => header[0] === 'set-cookie').map((header) => {
    //     const tmpCookie = header[1].split(';')[0]
    //     const firstEqual = tmpCookie.indexOf('=')
    //     return [tmpCookie.slice(0, firstEqual), tmpCookie.slice(firstEqual + 1)]
    // }))).catch(error => ({}))
    //
    // cookie = { ...cookie, ...await JsInstCookie }

    const LoginJsInstrumentationSubtask = await sendLoginRequest(bearer_token, guest_token, cookie, headers, new URLSearchParams({}), {
        flow_token: response.flow_token,
        subtask_inputs: [
            {
                js_instrumentation: {
                    link: 'next_link',
                    response: '{}'
                },
                subtask_id: 'LoginJsInstrumentationSubtask'
            }
        ]
    })

    cookie = { ...cookie, ...LoginJsInstrumentationSubtask.cookies }
    response = LoginJsInstrumentationSubtask.content
}

// LoginEnterUserIdentifierSSO
const LoginEnterUserIdentifierSSO = await sendLoginRequest(bearer_token, guest_token, cookie, headers, new URLSearchParams({}), {
    flow_token: response.flow_token,
    subtask_inputs: [
        {
            settings_list: {
                link: 'next_link',
                setting_responses: [
                    {
                        key: 'user_identifier',
                        response_data: {
                            text_data: {
                                result: account
                            }
                        }
                    }
                ]
            },
            subtask_id: 'LoginEnterUserIdentifierSSO'
        }
    ]
})

cookie = { ...cookie, ...LoginEnterUserIdentifierSSO.cookies }
response = LoginEnterUserIdentifierSSO.content

// LoginEnterAlternateIdentifierSubtask
if (LoginEnterUserIdentifierSSO.content.subtasks[0]?.subtask_id === 'LoginEnterAlternateIdentifierSubtask') {
    const LoginEnterAlternateIdentifierSubtask = await sendLoginRequest(bearer_token, guest_token, cookie, headers, new URLSearchParams({}), {
        flow_token: response.flow_token,
        subtask_inputs: [
            {
                enter_text: {
                    link: 'next_link',
                    text: screen_name // or phone number
                },
                subtask_id: 'LoginEnterAlternateIdentifierSubtask'
            }
        ]
    })

    cookie = { ...cookie, ...LoginEnterAlternateIdentifierSubtask.cookies }
    response = LoginEnterAlternateIdentifierSubtask.content
}

// LoginEnterPassword

const LoginEnterPassword = await sendLoginRequest(bearer_token, guest_token, cookie, headers, new URLSearchParams({}), {
    flow_token: response.flow_token,
    subtask_inputs: [
        {
            enter_password: {
                link: 'next_link',
                password
            },
            subtask_id: 'LoginEnterPassword'
        }
    ]
})

cookie = { ...cookie, ...LoginEnterPassword.cookies }
response = LoginEnterPassword.content

// AccountDuplicationCheck
const AccountDuplicationCheck = await sendLoginRequest(bearer_token, guest_token, cookie, headers, new URLSearchParams({}), {
    flow_token: response.flow_token,
    subtask_inputs: [
        {
            check_logged_in_account: {
                link: 'AccountDuplicationCheck_false'
            },
            subtask_id: 'AccountDuplicationCheck'
        }
    ]
})

cookie = { ...cookie, ...AccountDuplicationCheck.cookies }
response = AccountDuplicationCheck.content

if (AccountDuplicationCheck.content.subtasks[0]?.subtask_id === 'LoginTwoFactorAuthChallenge') {
    // LoginTwoFactorAuthChooseMethod
    if (!AccountDuplicationCheck.content.subtasks[0]?.enter_text) {
        const LoginTwoFactorAuthChooseMethod = await sendLoginRequest(bearer_token, guest_token, cookie, headers, new URLSearchParams({}), {
            flow_token: response.flow_token,
            subtask_inputs: [
                {
                    choice_selection: {
                        link: 'next_link',
                        selected_choices: ['0']
                    },
                    subtask_id: 'LoginTwoFactorAuthChooseMethod'
                }
            ]
        })

        cookie = { ...cookie, ...LoginTwoFactorAuthChooseMethod.cookies }
        response = LoginTwoFactorAuthChooseMethod.content
    }

    // LoginTwoFactorAuthChallenge
    const LoginTwoFactorAuthChallenge = await sendLoginRequest(bearer_token, guest_token, cookie, headers, new URLSearchParams({}), {
        flow_token: response.flow_token,
        subtask_inputs: [
            {
                enter_text: {
                    link: 'next_link',
                    text: _2fa
                },
                subtask_id: 'LoginTwoFactorAuthChallenge'
            }
        ]
    })

    cookie = { ...cookie, ...LoginTwoFactorAuthChallenge.cookies }
    response = LoginTwoFactorAuthChallenge.content
}
// LoginAcid

if (AccountDuplicationCheck.content.subtasks[0]?.subtask_id === 'LoginAcid') {
    const LoginAcid = await sendLoginRequest(bearer_token, guest_token, cookie, headers, new URLSearchParams({}), {
        flow_token: response.flow_token,
        subtask_inputs: [
            {
                enter_text: {
                    text: acid,
                    link: 'next_link'
                },
                subtask_id: 'LoginAcid'
            }
        ]
    })

    cookie = { ...cookie, ...LoginAcid.cookies }
    response = LoginAcid.content
}

if (isAndroid) {
    console.log(JSON.stringify(response, null, 4)) // <- user info && oauth token/oauth token secret
} else {
    const viewer = await getViewer(bearer_token, cookie, 'qevmDaYaF66EOtboiNoQbQ', {
        responsive_web_graphql_exclude_directive_enabled: true,
        verified_phone_label_enabled: false,
        creator_subscriptions_tweet_preview_api_enabled: true,
        responsive_web_graphql_skip_user_profile_image_extensions_enabled: false,
        responsive_web_graphql_timeline_navigation_enabled: true
    })
    cookie = { ...cookie, ...viewer.cookies }

    console.log(JSON.stringify(viewer, null, 4)) // <- user info
    console.log(JSON.stringify(cookie, null, 4)) // <- cookies
}
