#!/usr/bin/env node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const index_js_1 = require("@modelcontextprotocol/sdk/server/index.js");
const index_js_2 = require("@modelcontextprotocol/sdk/client/index.js");
const types_js_1 = require("@modelcontextprotocol/sdk/types.js");
const chrome_mcp_shared_1 = require("chrome-mcp-shared");
const stdio_js_1 = require("@modelcontextprotocol/sdk/server/stdio.js");
const streamableHttp_js_1 = require("@modelcontextprotocol/sdk/client/streamableHttp.js");

let stdioMcpServer = null;
const browserConnections = new Map();

// 多浏览器配置
const BROWSER_CONFIGS = [
    { id: 'browser1', port: 12306, name: 'Browser 1' },
    { id: 'browser2', port: 12307, name: 'Browser 2' },
    { id: 'browser3', port: 12308, name: 'Browser 3' },
    { id: 'browser4', port: 12309, name: 'Browser 4' }
];

// 初始化浏览器连接
async function initializeBrowserConnections() {
    console.log('Initializing multi-browser connections...');
    
    for (const config of BROWSER_CONFIGS) {
        try {
            const client = new index_js_2.Client({ 
                name: `Multi Browser Proxy ${config.id}`, 
                version: '1.0.0' 
            }, { capabilities: {} });
            
            const transport = new streamableHttp_js_1.StreamableHTTPClientTransport(
                new URL(`http://127.0.0.1:${config.port}/mcp`), 
                {}
            );
            
            await client.connect(transport);
            
            browserConnections.set(config.id, {
                client,
                config,
                isConnected: true,
                lastSeen: Date.now()
            });
            
            console.log(`✅ Connected to ${config.name} on port ${config.port}`);
        } catch (error) {
            console.log(`❌ Failed to connect to ${config.name} on port ${config.port}: ${error.message}`);
        }
    }
    
    console.log(`Successfully connected to ${browserConnections.size} browsers`);
}

// 获取所有浏览器信息
async function getAllBrowsersInfo() {
    const allBrowsersInfo = [];
    
    for (const [browserId, connection] of browserConnections) {
        if (!connection.isConnected) continue;
        
        try {
            const result = await connection.client.callTool({
                name: 'get_windows_and_tabs',
                arguments: {}
            });
            
            const browserInfo = {
                browserId,
                browserName: connection.config.name,
                port: connection.config.port,
                ...result,
                windows: result.windows?.map(window => ({
                    ...window,
                    browserId,
                    globalWindowId: `${browserId}_${window.windowId}`
                }))
            };
            
            allBrowsersInfo.push(browserInfo);
        } catch (error) {
            console.error(`Failed to get info from ${browserId}:`, error);
            connection.isConnected = false;
        }
    }
    
    return {
        totalBrowsers: allBrowsersInfo.length,
        connectedBrowsers: Array.from(browserConnections.keys()),
        browsers: allBrowsersInfo
    };
}

// 路由工具调用到指定浏览器
async function routeToolCall(toolName, args) {
    const browserId = args.browserId || args.browser_id;
    const windowId = args.windowId || args.window_id;
    
    let targetConnection = null;
    
    // 1. 如果指定了browserId
    if (browserId) {
        targetConnection = browserConnections.get(browserId);
    }
    // 2. 如果指定了windowId，查找包含该窗口的浏览器
    else if (windowId) {
        targetConnection = await findBrowserByWindowId(windowId);
    }
    // 3. 使用第一个可用的浏览器
    else {
        for (const connection of browserConnections.values()) {
            if (connection.isConnected) {
                targetConnection = connection;
                break;
            }
        }
    }
    
    if (!targetConnection || !targetConnection.isConnected) {
        throw new Error('No available browser connection found');
    }
    
    // 清理参数（移除路由相关参数）
    const cleanArgs = { ...args };
    delete cleanArgs.browserId;
    delete cleanArgs.browser_id;
    delete cleanArgs.windowId;
    delete cleanArgs.window_id;
    
    // 执行工具调用
    const result = await targetConnection.client.callTool({
        name: toolName,
        arguments: cleanArgs
    });
    
    return {
        ...result,
        executedOn: {
            browserId: targetConnection.config.id,
            browserName: targetConnection.config.name,
            port: targetConnection.config.port
        }
    };
}

// 根据窗口ID查找浏览器
async function findBrowserByWindowId(windowId) {
    for (const [browserId, connection] of browserConnections) {
        if (!connection.isConnected) continue;
        
        try {
            const result = await connection.client.callTool({
                name: 'get_windows_and_tabs',
                arguments: {}
            });
            
            const hasWindow = result.windows?.some(window => window.windowId === windowId);
            if (hasWindow) {
                return connection;
            }
        } catch (error) {
            console.error(`Error checking windows in ${browserId}:`, error);
        }
    }
    
    return null;
}

// 创建增强的工具模式
function createEnhancedToolSchemas() {
    const enhancedTools = [
        {
            name: 'get_all_browsers_info',
            description: 'Get information about all connected browsers and their windows/tabs',
            inputSchema: {
                type: 'object',
                properties: {},
                required: []
            }
        }
    ];
    
    // 为每个原始工具添加浏览器选择参数
    const originalTools = chrome_mcp_shared_1.TOOL_SCHEMAS.map(tool => ({
        ...tool,
        inputSchema: {
            ...tool.inputSchema,
            properties: {
                ...tool.inputSchema.properties,
                browserId: {
                    type: 'string',
                    description: 'Browser ID to execute the command on (browser1, browser2, browser3, browser4)',
                    enum: ['browser1', 'browser2', 'browser3', 'browser4']
                }
            }
        }
    }));
    
    return [...enhancedTools, ...originalTools];
}
