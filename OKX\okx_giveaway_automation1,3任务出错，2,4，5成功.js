#!/usr/bin/env node

/**
 * OKX Giveaway 自动化脚本
 *
 * 基于真实抓包数据实现的自动化任务完成脚本
 * 使用 Puppeteer 无头浏览器完成 Jasper Vault Giveaway 任务
 * 默认模式：有私钥 + 无头浏览器
 */

import puppeteer from 'puppeteer';
import fs from 'fs';
import { Wallet } from 'ethers';
import { OKXWalletConnectorSimple } from './okx_wallet_connect_simple_headless.js';

console.log('🎯 OKX Giveaway 自动化脚本');
console.log('📅 基于抓包数据: 2025-01-05');

class OKXGiveawayAutomation {
    constructor(privateKey, twitterTokens) {
        this.baseUrl = 'https://web3.okx.com/zh-hans/giveaway/jaspervault';
        this.browser = null;
        this.page = null;
        this.privateKey = privateKey;
        this.twitterTokens = twitterTokens;
        this.walletConnector = null;
        this.walletAddress = null;
        this.taskSelectors = {
            task1: 'body > div:nth-of-type(1) > div > div > div > div > div:nth-of-type(2) > div:nth-of-type(2) > div > div:nth-of-type(3) > div:nth-of-type(1) > button',
            task2: 'body > div:nth-of-type(1) > div > div > div > div > div:nth-of-type(2) > div:nth-of-type(2) > div > div:nth-of-type(4) > div > button',
            task3: 'body > div:nth-of-type(1) > div > div > div > div > div:nth-of-type(2) > div:nth-of-type(2) > div > div:nth-of-type(5) > div > button',
            task4: 'body > div:nth-of-type(1) > div > div > div > div > div:nth-of-type(2) > div:nth-of-type(2) > div > div:nth-of-type(6) > div > button',
            task5: 'body > div:nth-of-type(1) > div > div > div > div > div:nth-of-type(2) > div:nth-of-type(2) > div > div:nth-of-type(7) > div > button',
            verifyButton: 'body > div:nth-of-type(1) > div > div > div > div > div:nth-of-type(2) > div:nth-of-type(3) > div > div:nth-of-type(2) > button',
            twitterAuthorize: '[data-testid="OAuth_Consent_Button"]'
        };
    }

    // 初始化无头浏览器
    async initBrowser() {
        console.log('🚀 启动无头浏览器...');

        this.browser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu'
            ]
        });

        this.page = await this.browser.newPage();
        
        // 设置用户代理
        await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36');
        
        // 设置视口
        await this.page.setViewport({ width: 1280, height: 720 });
        
        console.log('✅ 无头浏览器初始化成功');
    }

    // 关闭浏览器
    async closeBrowser() {
        if (this.browser) {
            await this.browser.close();
            console.log('🔒 无头浏览器已关闭');
        }
    }

    // 延迟函数
    async delay(ms) {
        console.log(`⏸️ 等待 ${ms/1000} 秒...`);
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 生成钱包地址
    generateWalletAddress(privateKey) {
        try {
            if (!privateKey.startsWith('0x')) {
                privateKey = '0x' + privateKey;
            }
            const wallet = new Wallet(privateKey);
            return wallet.address;
        } catch (error) {
            console.error('❌ 生成钱包地址失败:', error.message);
            throw error;
        }
    }

    // 连接钱包
    async connectWallet() {
        console.log('\n💼 步骤0: 连接 OKX 钱包');

        try {
            // 生成钱包地址
            this.walletAddress = this.generateWalletAddress(this.privateKey);
            console.log(`💼 钱包地址: ${this.walletAddress}`);

            // 初始化钱包连接器
            this.walletConnector = new OKXWalletConnectorSimple();

            // 使用现有的浏览器实例
            this.walletConnector.browser = this.browser;
            this.walletConnector.page = this.page;

            // 执行钱包连接
            const connectResult = await this.walletConnector.connectWallet(this.privateKey, '自动化钱包');

            if (connectResult.success) {
                console.log('✅ 钱包连接成功');
                return true;
            } else {
                console.error('❌ 钱包连接失败:', connectResult.error);
                return false;
            }

        } catch (error) {
            console.error('❌ 钱包连接过程失败:', error.message);
            return false;
        }
    }

    // 悬停在元素上
    async hoverElement(selector, description) {
        try {
            console.log(`🖱️ 悬停: ${description}`);

            // 等待元素出现
            await this.page.waitForSelector(selector, { timeout: 10000 });

            // 使用JavaScript触发悬停事件
            await this.page.evaluate((selector) => {
                const element = document.querySelector(selector);
                if (element) {
                    const mouseEnterEvent = new MouseEvent('mouseenter', {
                        view: window,
                        bubbles: true,
                        cancelable: true
                    });
                    const mouseOverEvent = new MouseEvent('mouseover', {
                        view: window,
                        bubbles: true,
                        cancelable: true
                    });
                    element.dispatchEvent(mouseEnterEvent);
                    element.dispatchEvent(mouseOverEvent);
                    return true;
                }
                return false;
            }, selector);

            await this.delay(3000); // 等待悬停效果显示
            return true;
        } catch (error) {
            console.error(`❌ 悬停失败: ${description}`, error.message);
            return false;
        }
    }

    // 点击元素
    async clickElement(selector, description, waitForNavigation = false) {
        try {
            console.log(`🖱️ 点击: ${description}`);
            
            if (waitForNavigation) {
                await Promise.all([
                    this.page.waitForNavigation({ waitUntil: 'domcontentloaded', timeout: 10000 }),
                    this.page.click(selector)
                ]);
            } else {
                await this.page.click(selector);
            }
            
            await this.delay(1000);
            return true;
        } catch (error) {
            console.error(`❌ 点击失败: ${description}`, error.message);
            return false;
        }
    }

    // 等待并点击按钮（通过文本查找）
    async clickButtonByText(text, description) {
        try {
            console.log(`🔍 查找按钮: ${description}`);
            await this.page.waitForFunction(
                (text) => {
                    const buttons = Array.from(document.querySelectorAll('button'));
                    return buttons.find(btn => btn.textContent.trim().includes(text));
                },
                { timeout: 5000 },
                text
            );

            const button = await this.page.evaluateHandle((text) => {
                const buttons = Array.from(document.querySelectorAll('button'));
                return buttons.find(btn => btn.textContent.trim().includes(text));
            }, text);

            if (button) {
                await button.click();
                console.log(`✅ 成功点击: ${description}`);
                await this.delay(1000);
                return true;
            }
            return false;
        } catch (error) {
            console.error(`❌ 查找/点击按钮失败: ${description}`, error.message);
            return false;
        }
    }

    // 绑定推特账号
    async bindTwitterAccount() {
        console.log('\n🐦 步骤1: 绑定推特账号');

        try {
            // 导航到页面
            console.log('🌐 导航到 Giveaway 页面...');
            await this.page.goto(this.baseUrl, {
                waitUntil: 'domcontentloaded',
                timeout: 60000
            });

            // 设置 Twitter cookies 和认证信息
            if (this.twitterTokens && this.twitterTokens.tokens) {
                console.log('🔑 设置 Twitter 认证信息...');

                const tokens = this.twitterTokens.tokens;

                // 设置 Twitter cookies
                if (tokens.cookies) {
                    const twitterCookies = [];

                    for (const [name, value] of Object.entries(tokens.cookies)) {
                        twitterCookies.push({
                            name: name,
                            value: value,
                            domain: '.x.com',
                            path: '/',
                            httpOnly: false,
                            secure: true
                        });

                        // 同时设置到 twitter.com 域名
                        twitterCookies.push({
                            name: name,
                            value: value,
                            domain: '.twitter.com',
                            path: '/',
                            httpOnly: false,
                            secure: true
                        });
                    }

                    // 先导航到 Twitter 域名设置 cookies
                    await this.page.goto('https://x.com', { waitUntil: 'domcontentloaded' });

                    for (const cookie of twitterCookies) {
                        try {
                            await this.page.setCookie(cookie);
                        } catch (cookieError) {
                            console.log(`⚠️ 设置 cookie ${cookie.name} 失败: ${cookieError.message}`);
                        }
                    }

                    console.log(`✅ 已设置 ${Object.keys(tokens.cookies).length} 个 Twitter cookies`);
                }

                // 设置 localStorage 数据
                if (tokens.localStorage && Object.keys(tokens.localStorage).length > 0) {
                    await this.page.evaluate((localStorageData) => {
                        for (const [key, value] of Object.entries(localStorageData)) {
                            localStorage.setItem(key, value);
                        }
                    }, tokens.localStorage);

                    console.log(`✅ 已设置 ${Object.keys(tokens.localStorage).length} 个 localStorage 项`);
                }

                // 返回到 OKX 页面
                await this.page.goto(this.baseUrl, {
                    waitUntil: 'domcontentloaded',
                    timeout: 60000
                });
            }

            // 悬停在第一个任务上
            await this.hoverElement(this.taskSelectors.task1, '第一个任务');

            // 点击连接按钮
            const connectSuccess = await this.clickButtonByText('连接', '连接推特按钮');
            if (!connectSuccess) {
                throw new Error('未找到连接按钮');
            }

            // 如果有 auth token，尝试自动处理授权
            if (this.authToken) {
                console.log('🔄 使用 Auth Token 自动授权...');

                // 等待一下看是否需要跳转
                await this.delay(3000);

                const currentUrl = this.page.url();
                if (currentUrl.includes('x.com') || currentUrl.includes('twitter.com')) {
                    console.log('🔗 检测到 Twitter 授权页面，尝试自动授权...');

                    // 注入 auth token 到 Twitter 页面
                    await this.page.evaluate((token) => {
                        // 尝试设置各种可能的认证信息
                        localStorage.setItem('auth_token', token);
                        localStorage.setItem('twitter_auth', token);
                        document.cookie = `auth_token=${token}; domain=.x.com; path=/`;
                        document.cookie = `auth_token=${token}; domain=.twitter.com; path=/`;
                    }, this.authToken);

                    // 尝试点击授权按钮
                    try {
                        await this.clickElement(this.taskSelectors.twitterAuthorize, 'Twitter 授权按钮', true);
                    } catch (authError) {
                        console.log('⚠️ 自动授权失败，可能需要手动处理');
                    }

                    // 等待回到 OKX 页面
                    await this.page.waitForFunction(
                        () => window.location.href.includes('web3.okx.com'),
                        { timeout: 15000 }
                    );
                }
            } else {
                console.log('⚠️ 未提供 Auth Token，需要手动授权');
                // 等待手动授权完成
                await this.page.waitForFunction(
                    () => window.location.href.includes('web3.okx.com'),
                    { timeout: 60000 }
                );
            }

            console.log('✅ 推特账号绑定成功');
            return true;

        } catch (error) {
            console.error('❌ 推特账号绑定失败:', error.message);
            return false;
        }
    }

    // 调用真实的OKX API
    async callOKXAPI(endpoint, data) {
        try {
            // 添加API调用前的延迟，模拟真实用户行为
            await this.delay(1000 + Math.random() * 2000); // 1-3秒随机延迟

            const timestamp = Date.now().toString();
            console.log(`🔗 API调用: ${endpoint} (时间戳: ${timestamp})`);

            const response = await this.page.evaluate(async (endpoint, data, timestamp) => {
                const url = `https://web3.okx.com/priapi/v1/dapp/giveaway/${endpoint}?t=${timestamp}`;

                const headers = {
                    'Accept': 'application/json',
                    'App-Type': 'web',
                    'Content-Type': 'application/json',
                    'Referer': 'https://web3.okx.com/zh-hans/giveaway/jaspervault',
                    'X-Cdn': 'https://web3.okx.com',
                    'X-Locale': 'zh_CN',
                    'X-Utc': '8'
                };

                const response = await fetch(url, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(data)
                });

                return {
                    status: response.status,
                    data: await response.json()
                };
            }, endpoint, data, timestamp);

            // API调用后的延迟
            await this.delay(500 + Math.random() * 1000); // 0.5-1.5秒延迟

            return response;
        } catch (error) {
            console.error(`❌ API调用失败 ${endpoint}:`, error.message);
            return { status: 500, data: { error: error.message } };
        }
    }

    // 执行单个任务
    async executeTask(taskNumber, taskName, needsTwitterRedirect = false) {
        console.log(`\n📋 步骤${taskNumber + 1}: ${taskName}`);

        try {
            const taskId = 618 + taskNumber; // 任务ID从618开始
            const taskSelector = this.taskSelectors[`task${taskNumber + 1}`];

            // 悬停在任务上
            await this.hoverElement(taskSelector, `任务${taskNumber + 1}`);

            // 悬停后等待，确保按钮显示
            await this.delay(2000 + Math.random() * 1000); // 2-3秒等待

            // 先尝试API调用
            console.log(`🔗 调用API开始任务${taskNumber + 1}...`);
            const apiResponse = await this.callOKXAPI('clickTask', {
                giveawayId: 389,
                taskId: taskId,
                walletAddress: this.walletAddress
            });

            if (apiResponse.status === 200 && apiResponse.data.code === 0) {
                console.log(`✅ 任务${taskNumber + 1} API调用成功`);

                // API成功后等待页面响应
                await this.delay(1500 + Math.random() * 1000); // 1.5-2.5秒等待
            } else {
                console.log(`⚠️ 任务${taskNumber + 1} API调用失败，尝试UI操作...`);

                // 点击开始按钮
                const startSuccess = await this.clickButtonByText('现在开始', `任务${taskNumber + 1}开始按钮`);
                if (!startSuccess) {
                    console.log(`⚠️ 任务${taskNumber + 1}可能已经完成或无需开始`);
                }

                // UI操作后等待
                await this.delay(2000 + Math.random() * 1000); // 2-3秒等待
            }
            
            // 如果需要 Twitter 重定向，处理跳转
            if (needsTwitterRedirect) {
                try {
                    // 等待可能的页面跳转
                    await this.delay(2000 + Math.random() * 1000); // 2-3秒等待跳转

                    await this.page.waitForNavigation({ waitUntil: 'domcontentloaded', timeout: 8000 });
                    const currentUrl = this.page.url();

                    if (currentUrl.includes('x.com') || currentUrl.includes('twitter.com')) {
                        console.log('🔗 已跳转到 Twitter 页面');

                        // 在Twitter页面停留一段时间，模拟真实用户行为
                        await this.delay(3000 + Math.random() * 2000); // 3-5秒停留

                        // 返回到 OKX 页面
                        await this.page.goto(this.baseUrl, { waitUntil: 'domcontentloaded' });
                        console.log('🔙 已返回到 OKX 页面');

                        // 返回后等待页面稳定
                        await this.delay(2000 + Math.random() * 1000); // 2-3秒等待
                    }
                } catch (navError) {
                    console.log('📝 任务未发生页面跳转');
                }
            }

            // 查找并点击刷新按钮
            try {
                const refreshButton = await this.page.$('i.icon.iconfont.index_verify__jMqrV.okx-defi-nft-filter-refresh.dc-a11y-button');
                if (refreshButton) {
                    await refreshButton.click();
                    console.log(`🔄 已刷新任务${taskNumber + 1}状态`);
                    await this.delay(2000 + Math.random() * 1000); // 2-3秒等待刷新
                }
            } catch (refreshError) {
                console.log(`📝 任务${taskNumber + 1}无刷新按钮或已完成`);
            }

            console.log(`✅ 任务${taskNumber + 1}执行完成`);

            // 任务间隔延迟，模拟真实用户操作节奏
            await this.delay(3000 + Math.random() * 2000); // 3-5秒任务间隔

            return true;
            
        } catch (error) {
            console.error(`❌ 任务${taskNumber + 1}执行失败:`, error.message);
            return false;
        }
    }

    // 检查任务状态
    async checkTaskStatus() {
        try {
            // 先尝试API检查
            console.log('🔗 调用API检查所有任务状态...');
            const apiResponse = await this.callOKXAPI('task/checkAll', {
                giveawayId: 389,
                walletAddress: { "8453": this.walletAddress },
                walletAccountId: "BB9AB302-2EA1-4625-85E2-89F267E3D624", // 这个需要从钱包连接获取
                userUniqueId: ""
            });

            if (apiResponse.status === 200 && apiResponse.data.code === 0) {
                console.log('✅ API任务状态检查成功');
                const apiData = apiResponse.data.data;
                const taskStatuses = [];
                let completedCount = 0;

                // 解析API返回的任务状态
                for (let taskId = 618; taskId <= 622; taskId++) {
                    const taskStatus = apiData[taskId.toString()];
                    const isCompleted = taskStatus && taskStatus.status === 1;
                    if (isCompleted) completedCount++;

                    taskStatuses.push({
                        taskNumber: taskId - 617,
                        taskId: taskId,
                        taskTitle: this.getTaskName(taskId),
                        isCompleted: isCompleted,
                        status: isCompleted ? '✅ 已完成' : '⏳ 进行中'
                    });
                }

                console.log('\n📊 任务状态检查 (API):');
                taskStatuses.forEach(task => {
                    console.log(`   ${task.taskNumber}. ${task.taskTitle}: ${task.status}`);
                });

                console.log(`\n📈 完成进度: ${completedCount}/${taskStatuses.length}`);
                return { taskStatuses, completedCount, totalTasks: taskStatuses.length };
            }

            // API失败时回退到UI检查
            console.log('⚠️ API检查失败，使用UI检查...');
            const taskStatuses = await this.page.evaluate(() => {
                const tasks = document.querySelectorAll('.index_task__WvAcy');
                return Array.from(tasks).map((task, index) => {
                    const isCompleted = task.classList.contains('index_task-completed-dark__nBhdY');
                    const hasSuccessIcon = task.querySelector('.okds-success-circle-fill') !== null;
                    const taskTitle = task.querySelector('.index_task-title__AWVbD')?.textContent || `任务${index + 1}`;

                    return {
                        taskNumber: index + 1,
                        taskTitle,
                        isCompleted: isCompleted || hasSuccessIcon,
                        status: isCompleted || hasSuccessIcon ? '✅ 已完成' : '⏳ 进行中'
                    };
                });
            });

            console.log('\n📊 任务状态检查 (UI):');
            taskStatuses.forEach(task => {
                console.log(`   ${task.taskNumber}. ${task.taskTitle}: ${task.status}`);
            });

            const completedCount = taskStatuses.filter(task => task.isCompleted).length;
            console.log(`\n📈 完成进度: ${completedCount}/${taskStatuses.length}`);

            return { taskStatuses, completedCount, totalTasks: taskStatuses.length };

        } catch (error) {
            console.error('❌ 检查任务状态失败:', error.message);
            return { taskStatuses: [], completedCount: 0, totalTasks: 0 };
        }
    }

    // 获取任务名称
    getTaskName(taskId) {
        const taskNames = {
            618: "关注 @Jaspervault 的 X",
            619: "加入 Jasper Vault 官方 Discord 社区",
            620: "连接 OKX Wallet 并参与活动",
            621: "关注 @wallet 的 X",
            622: "OKX Wallet 持有至少 10 USDT 等值代币"
        };
        return taskNames[taskId] || `任务${taskId}`;
    }

    // 验证任务完成
    async verifyTasks() {
        console.log('\n🎯 步骤6: 验证任务完成');

        try {
            // 先尝试API验证
            console.log('🔗 调用API验证任务完成...');
            const apiResponse = await this.callOKXAPI('verify', {
                giveawayId: 389,
                walletAddress: this.walletAddress,
                walletAccountId: "BB9AB302-2EA1-4625-85E2-89F267E3D624", // 这个需要从钱包连接获取
                userUniqueId: ""
            });

            if (apiResponse.status === 200 && apiResponse.data.code === 0) {
                const verifyData = apiResponse.data.data;
                if (verifyData.verifySucceed) {
                    console.log('🎉 API验证成功! 所有任务已完成');
                    return true;
                } else {
                    console.log(`❌ API验证失败: 失败原因代码 ${verifyData.verifyFailCause}`);
                    return false;
                }
            }

            // API失败时回退到UI验证
            console.log('⚠️ API验证失败，尝试UI验证...');
            await this.clickElement(this.taskSelectors.verifyButton, '验证按钮');
            console.log('✅ 验证请求已发送');

            // 等待验证结果
            await this.delay(3000);

            return true;
        } catch (error) {
            console.error('❌ 验证失败:', error.message);
            return false;
        }
    }

    // 完整的 Giveaway 流程
    async completeGiveaway() {
        console.log('\n🎯 开始 OKX Giveaway 自动化流程');
        console.log('='.repeat(60));

        const results = {
            startTime: new Date().toISOString(),
            walletAddress: this.walletAddress,
            twitterToken: this.twitterTokens ? this.twitterTokens.timestamp || 'N/A' : 'N/A',
            steps: {},
            success: false
        };

        try {
            // 步骤0: 连接钱包
            results.steps.walletConnection = await this.connectWallet();
            await this.delay(3000);

            // 步骤1: 绑定推特账号
            results.steps.twitterBinding = await this.bindTwitterAccount();
            if (!results.steps.twitterBinding) {
                throw new Error('推特账号绑定失败');
            }

            await this.delay(3000);
            
            // 步骤2-5: 执行各个任务
            const tasks = [
                { name: '关注 @Jaspervault 的 X', needsRedirect: true },
                { name: '加入 Jasper Vault 官方 Discord 社区', needsRedirect: false },
                { name: '连接 OKX Wallet 并参与活动', needsRedirect: false },
                { name: '关注 @wallet 的 X', needsRedirect: true },
                { name: 'OKX Wallet 持有至少 10 USDT 等值代币', needsRedirect: false }
            ];
            
            for (let i = 0; i < tasks.length; i++) {
                const task = tasks[i];
                console.log(`\n⏳ 准备执行任务 ${i + 1}/${tasks.length}: ${task.name}`);

                results.steps[`task${i + 1}`] = await this.executeTask(i, task.name, task.needsRedirect);

                // 任务间更长的延迟，避免被检测为机器人
                console.log(`⏸️ 任务间隔等待...`);
                await this.delay(4000 + Math.random() * 3000); // 4-7秒任务间隔
            }

            // 所有任务完成后，等待一段时间再检查状态
            console.log('\n⏸️ 所有任务执行完成，等待状态更新...');
            await this.delay(5000 + Math.random() * 3000); // 5-8秒等待状态更新

            // 检查任务状态
            const statusCheck = await this.checkTaskStatus();
            results.taskStatus = statusCheck;

            // 状态检查后等待
            await this.delay(2000 + Math.random() * 1000); // 2-3秒等待

            // 步骤6: 验证任务完成
            if (statusCheck.completedCount >= 4) { // 至少4个任务完成
                console.log('\n⏸️ 准备验证任务完成...');
                await this.delay(3000 + Math.random() * 2000); // 3-5秒验证前等待

                results.steps.verification = await this.verifyTasks();
            } else {
                console.log('⚠️ 任务完成数量不足，跳过验证步骤');
                results.steps.verification = false;
            }
            
            results.success = Object.values(results.steps).filter(Boolean).length >= 4;
            results.endTime = new Date().toISOString();
            
            console.log('\n🎉 Giveaway 流程完成！');
            console.log('='.repeat(60));
            
            return results;
            
        } catch (error) {
            console.error('❌ Giveaway 流程失败:', error.message);
            results.error = error.message;
            results.endTime = new Date().toISOString();
            return results;
        }
    }
}

// 从文件读取私钥
function loadPrivateKeys(filename = 'private_keys.txt') {
    try {
        if (!fs.existsSync(filename)) {
            console.log(`⚠️ 私钥文件 ${filename} 不存在`);
            return [];
        }

        const content = fs.readFileSync(filename, 'utf8');
        const lines = content.split('\n');
        const privateKeys = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            if (!line || line.startsWith('#')) continue;

            const cleanKey = line.replace('0x', '');
            if (/^[a-fA-F0-9]{64}$/.test(cleanKey)) {
                privateKeys.push(line.startsWith('0x') ? line : '0x' + line);
            }
        }

        console.log(`📖 读取到 ${privateKeys.length} 个有效私钥`);
        return privateKeys;

    } catch (error) {
        console.error(`❌ 读取私钥文件失败: ${error.message}`);
        return [];
    }
}

// 从文件读取 Twitter Tokens (JSON 格式)
function loadTwitterTokens(filename = 'twitter_tokens.json') {
    try {
        if (!fs.existsSync(filename)) {
            console.log(`⚠️ Twitter Tokens 文件 ${filename} 不存在`);
            return [];
        }

        const content = fs.readFileSync(filename, 'utf8');

        // 尝试解析为单个 JSON 对象
        try {
            const singleToken = JSON.parse(content);
            if (singleToken.tokens && singleToken.tokens.cookies) {
                console.log(`📖 读取到 1 个 Twitter Token (单个文件)`);
                return [singleToken];
            }
        } catch (e) {
            // 如果不是单个 JSON，尝试按行解析多个 JSON
        }

        // 按行解析多个 JSON 对象
        const lines = content.split('\n');
        const twitterTokens = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            if (!line || line.startsWith('#')) continue;

            try {
                const tokenData = JSON.parse(line);
                if (tokenData.tokens && tokenData.tokens.cookies) {
                    twitterTokens.push(tokenData);
                }
            } catch (parseError) {
                console.log(`⚠️ 第 ${i + 1} 行 JSON 解析失败: ${parseError.message}`);
            }
        }

        console.log(`📖 读取到 ${twitterTokens.length} 个有效 Twitter Token`);
        return twitterTokens;

    } catch (error) {
        console.error(`❌ 读取 Twitter Tokens 文件失败: ${error.message}`);
        return [];
    }
}

// 主函数
async function main() {
    console.log('🎯 OKX Giveaway 自动化脚本启动');

    // 读取私钥文件
    const privateKeys = loadPrivateKeys('private_keys.txt');

    if (privateKeys.length === 0) {
        console.error('❌ 未找到有效的私钥文件 private_keys.txt');
        console.log('� 请创建 private_keys.txt 文件，每行一个私钥');
        process.exit(1);
    }

    // 读取 Twitter Tokens 文件
    const twitterTokens = loadTwitterTokens('twitter_tokens.json');

    if (twitterTokens.length === 0) {
        console.error('❌ 未找到有效的 Twitter Tokens 文件 twitter_tokens.json');
        console.log('📝 请创建 twitter_tokens.json 文件，包含 Twitter 认证信息');
        process.exit(1);
    }

    // 检查私钥和 Twitter Tokens 数量是否匹配
    if (privateKeys.length !== twitterTokens.length) {
        console.error(`❌ 私钥数量 (${privateKeys.length}) 与 Twitter Tokens 数量 (${twitterTokens.length}) 不匹配`);
        console.log('📝 请确保私钥和 Twitter Tokens 数量相同');
        process.exit(1);
    }

    console.log(`✅ 成功加载 ${privateKeys.length} 个钱包私钥和 ${twitterTokens.length} 个 Twitter Tokens\n`);

    // 批量处理多个钱包
    for (let i = 0; i < privateKeys.length; i++) {
        const privateKey = privateKeys[i];
        const twitterToken = twitterTokens[i];
        const walletName = `钱包${i + 1}`;

        console.log(`\n📋 进度: ${i + 1}/${privateKeys.length} - ${walletName}`);
        console.log(`🔑 私钥: ${privateKey.substring(0, 10)}...`);
        console.log(`🐦 Twitter Token: ${twitterToken.timestamp || 'N/A'}`);

        const automation = new OKXGiveawayAutomation(privateKey, twitterToken);

        try {
            await automation.initBrowser();
            const result = await automation.completeGiveaway();

            // 保存结果
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `okx_giveaway_result_${walletName}_${timestamp}.json`;
            fs.writeFileSync(filename, JSON.stringify(result, null, 2));
            console.log(`💾 ${walletName} 结果已保存到: ${filename}`);

            // 显示结果摘要
            console.log(`\n📊 ${walletName} 执行结果摘要:`);
            console.log(`   钱包地址: ${result.walletAddress || 'N/A'}`);
            console.log(`   Twitter Token: ${result.twitterToken || 'N/A'}`);
            console.log(`   成功状态: ${result.success ? '✅ 成功' : '❌ 失败'}`);
            console.log(`   钱包连接: ${result.steps.walletConnection ? '✅' : '❌'}`);
            console.log(`   推特绑定: ${result.steps.twitterBinding ? '✅' : '❌'}`);
            console.log(`   任务完成: ${result.taskStatus?.completedCount || 0}/${result.taskStatus?.totalTasks || 5}`);
            console.log(`   验证状态: ${result.steps.verification ? '✅' : '❌'}`);

            if (result.error) {
                console.log(`   错误信息: ${result.error}`);
            }

        } finally {
            await automation.closeBrowser();
        }

        // 避免请求过于频繁
        if (i < privateKeys.length - 1) {
            console.log('⏸️ 等待10秒后处理下一个钱包...');
            await new Promise(resolve => setTimeout(resolve, 10000));
        }
    }
}

// 直接运行主函数
main().catch(console.error);

export { OKXGiveawayAutomation };
