# OKX Giveaway 自动化脚本使用说明

## 📋 功能概述

这个自动化脚本能够完成 OKX Jasper Vault Giveaway 的所有任务：

1. **连接 OKX 钱包**（如果提供私钥）
2. **绑定推特账号**（Twitter OAuth 授权）
3. **完成5个任务**：
   - 关注 @Jaspervault 的 X
   - 加入 Jasper Vault 官方 Discord 社区
   - 连接 OKX Wallet 并参与活动
   - 关注 @wallet 的 X
   - OKX Wallet 持有至少 10 USDT 等值代币
4. **验证任务完成**

## 🚀 使用方法

1. 创建 `private_keys.txt` 文件，每行一个私钥：
```
0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
0x9876543210fedcba9876543210fedcba9876543210fedcba9876543210fedcba
# 这是注释行，会被忽略
```

2. 创建 `twitter_tokens.json` 文件，包含 Twitter 认证信息：

**单个账号模式**（一个 JSON 对象）：
```json
{
  "timestamp": "2025-01-05T12:00:00.000Z",
  "url": "https://x.com/home",
  "tokens": {
    "cookies": {
      "guest_id": "v1%3A171577395576583272",
      "ct0": "76d435bac7f20af2283e6eeaba5fdeeab3503293e172f2e0019a1ad449b5e7e456e3256861f8f6a605811dfa47a571b4c546adb1ab71faef840edb38c5a4e4559e9e26706f0a7f2b7b16af40705fbf2a",
      "twid": "u%3D512880732"
    },
    "localStorage": {},
    "sessionStorage": {}
  }
}
```

**多个账号模式**（每行一个 JSON 对象）：
```json
{"timestamp": "2025-01-05T12:00:00.000Z", "tokens": {"cookies": {"ct0": "token1", "twid": "u%3D123"}}}
{"timestamp": "2025-01-05T12:00:00.000Z", "tokens": {"cookies": {"ct0": "token2", "twid": "u%3D456"}}}
```

**重要：** Twitter tokens 的顺序必须与私钥文件中的顺序一致，每个钱包对应一个 Twitter 账号。

3. 运行脚本：
```bash
node okx_giveaway_automation.js
```

## 📁 文件说明

- `okx_giveaway_automation.js` - 主要的自动化脚本
- `okx_wallet_connect_simple_headless.js` - 钱包连接功能（被主脚本调用）
- `okx_giveaway_api_capture.json` - 抓包数据记录
- `private_keys.txt` - 私钥文件（需要自己创建）
- `twitter_tokens.json` - Twitter 认证信息文件（需要自己创建）
- `private_keys_example.txt` - 私钥文件示例
- `twitter_tokens_example.json` - Twitter tokens 文件示例

## 🔧 配置选项

在脚本中可以修改以下配置：

```javascript
// 调整等待时间
await this.delay(3000); // 3秒
```

## 📊 输出结果

脚本会生成详细的执行报告：

- `okx_giveaway_result_钱包1_[timestamp].json` - 单个钱包的执行结果

结果包含：
- 钱包地址
- 各步骤执行状态
- 任务完成情况
- 验证结果
- 错误信息（如有）

## 🐦 如何获取 Twitter 认证信息

### 方法1: 使用浏览器开发者工具

1. **登录 Twitter**：在浏览器中登录您要绑定的 Twitter 账号
2. **打开开发者工具**：按 F12 或右键选择"检查"
3. **进入 Application 标签**：点击开发者工具中的 "Application" 标签
4. **查看 Cookies**：在左侧找到 "Cookies" → "https://x.com"
5. **复制关键 Cookies**：复制以下重要的 cookie 值：
   - `ct0` - CSRF Token
   - `twid` - Twitter User ID
   - `guest_id` - Guest ID
   - `personalization_id` - 个性化ID

### 方法2: 使用现有的 twitter_tokens_144.json

如果您已经有类似 `twitter_tokens_144.json` 的文件，可以直接重命名为 `twitter_tokens.json` 使用。

### JSON 格式示例：
```json
{
  "timestamp": "2025-01-05T12:00:00.000Z",
  "tokens": {
    "cookies": {
      "ct0": "您的ct0值",
      "twid": "u%3D您的用户ID",
      "guest_id": "v1%3A您的guest_id"
    }
  }
}
```

## ⚠️ 注意事项

1. **私钥安全**：请确保私钥文件的安全，不要泄露给他人
2. **Twitter 认证安全**：Twitter cookies 等同于账号密码，请妥善保管
3. **文件对应关系**：确保私钥和 Twitter tokens 的顺序一致
4. **Token 时效性**：Twitter cookies 有时效性，过期后需要重新获取
5. **网络环境**：确保网络连接稳定，能够访问 Twitter 和 OKX
6. **执行频率**：脚本会自动在多个钱包之间添加延迟，避免请求过于频繁
7. **浏览器要求**：需要安装 Chrome 或 Chromium 浏览器

## 🐛 故障排除

### 常见问题

1. **Twitter 授权失败**
   - 检查网络连接
   - 确保能够访问 Twitter
   - 手动完成一次授权流程

2. **钱包连接失败**
   - 检查私钥格式是否正确
   - 确保私钥有效

3. **任务状态检查失败**
   - 页面加载可能较慢，脚本会自动重试
   - 检查 OKX 网站是否正常访问

### 调试模式

脚本默认运行在无头模式，如需调试可以修改代码中的 `headless: true` 为 `headless: false`。

## 📈 成功率优化

为了提高成功率：

1. 使用稳定的网络环境
2. 确保钱包有足够的余额（第5个任务需要至少10 USDT）
3. 在网络较好的时间段运行
4. 如果失败，可以重新运行脚本

## 🔄 重复执行

脚本支持重复执行，会自动检查任务状态：
- 已完成的任务会被跳过
- 未完成的任务会重新尝试
- 最终会尝试验证所有任务

## 📞 技术支持

如果遇到问题，请检查：
1. Node.js 版本（建议 18+）
2. 依赖包是否正确安装
3. 网络连接是否正常
4. 私钥格式是否正确
