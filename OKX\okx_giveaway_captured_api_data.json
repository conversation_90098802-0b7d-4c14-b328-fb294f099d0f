{"captureInfo": {"timestamp": "2025-08-06T22:29:00Z", "totalRequests": 23, "captureStartTime": 1754519347896, "captureEndTime": 1754519402841, "totalDurationMs": 54945, "tabUrl": "https://web3.okx.com/zh-hans/giveaway/jaspervault", "walletAddress": "******************************************"}, "keyApiEndpoints": {"clickTask": {"url": "https://web3.okx.com/priapi/v1/dapp/giveaway/clickTask", "method": "POST", "description": "点击任务API - 用于开始任务", "examples": [{"taskId": 618, "giveawayId": 389, "walletAddress": "******************************************"}, {"taskId": 619, "giveawayId": 389, "walletAddress": "******************************************"}, {"taskId": 620, "giveawayId": 389, "walletAddress": "******************************************"}]}, "taskCheck": {"url": "https://web3.okx.com/priapi/v1/dapp/giveaway/task/check", "method": "POST", "description": "检查单个任务状态", "example": {"taskId": 619, "giveawayId": 389, "walletAddress": "******************************************", "walletAccountId": "BB9AB302-2EA1-4625-85E2-89F267E3D624", "userUniqueId": ""}}, "verify": {"url": "https://web3.okx.com/priapi/v1/dapp/giveaway/verify", "method": "POST", "description": "验证所有任务完成", "example": {"giveawayId": 389, "walletAddress": "******************************************", "walletAccountId": "BB9AB302-2EA1-4625-85E2-89F267E3D624", "userUniqueId": ""}}, "getDetailV2": {"url": "https://web3.okx.com/priapi/v1/dapp/giveaway/getDetailV2", "method": "POST", "description": "获取Giveaway详情和任务列表", "example": {"navName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "walletAddress": {"8453": "******************************************"}}}, "checkAll": {"url": "https://web3.okx.com/priapi/v1/dapp/giveaway/task/checkAll", "method": "POST", "description": "检查所有任务状态", "example": {"giveawayId": 389, "walletAddress": {"8453": "******************************************"}, "walletAccountId": "BB9AB302-2EA1-4625-85E2-89F267E3D624", "userUniqueId": ""}}}, "commonHeaders": {"Accept": "application/json", "App-Type": "web", "Content-Type": "application/json", "Devid": "1a096c72-ad39-4768-a4fb-8440544ff843", "Referer": "https://web3.okx.com/zh-hans/giveaway/jaspervault", "X-Cdn": "https://web3.okx.com", "X-Discover-Auth-Token": "undefined", "X-Locale": "zh_CN", "X-Simulated-Trading": "undefined", "X-Site-Info": "==QfxojI5RXa05WZiwiIMFkQPx0Rfh1SPJiOiUGZvNmIsIyUVJiOi42bpdWZyJye", "X-Utc": "8", "X-Zkdex-Env": "0"}, "authenticationHeaders": {"Ok-Timestamp": "动态时间戳", "Ok-Verify-Sign": "动态签名", "Ok-Verify-Token": "动态令牌", "X-FpToken": "指纹令牌", "X-FpToken-Signature": "指纹签名", "X-Id-Group": "用户组ID", "X-Request-Timestamp": "请求时间戳"}, "taskIds": {"618": "关注 @Jaspervault 的 X", "619": "加入 Jasper Vault 官方 Discord 社区", "620": "连接 OKX Wallet 并参与活动", "621": "关注 @wallet 的 X", "622": "OKX Wallet 持有至少 10 USDT 等值代币"}, "giveawayInfo": {"id": 389, "name": "Jasper Vault 价值 50K USDT 代币奖励活动", "chainId": 8453, "winnerCount": 5000, "userCount": 653686, "endTime": 1754560800000, "startTime": 1753347600000}, "rawNetworkData": {"success": true, "message": "Capture for tab 159821864 (https://web3.okx.com/zh-hans/giveaway/jaspervault) stopped. 23 requests captured.", "tabId": 159821864, "tabUrl": "https://web3.okx.com/zh-hans/giveaway/jaspervault", "tabTitle": "Jasper Vault 价值 50K USDT 代币奖励活动 | 赢取 OKX Wallet x Jasper Vault Giveaway 空投 | OKX Wallet", "requestCount": 23, "captureStartTime": 1754519347896, "captureEndTime": 1754519402841, "totalDurationMs": 54945, "note": "完整的原始请求数据已保存，包含所有请求头、响应体和认证信息"}}