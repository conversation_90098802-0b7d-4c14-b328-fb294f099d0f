// OAuth 1.0a 签名工具
// 用于使用生成的推特账号进行 API 请求

import crypto from 'crypto';

// Twitter 客户端配置
const TWITTER_CONSUMER_KEY = '3nVuSoBZnx6U4vzUxf5w';
const TWITTER_CONSUMER_SECRET = 'Bcs59EFbbsdF6Sl9Ng71smgStWEGwXXKSjYvPVt7qys';

/**
 * 生成随机字符串
 * @param {number} length 长度
 * @returns {string} 随机字符串
 */
function generateNonce(length = 32) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

/**
 * URL 编码（符合 OAuth 规范）
 * @param {string} str 要编码的字符串
 * @returns {string} 编码后的字符串
 */
function oauthEncode(str) {
    return encodeURIComponent(str)
        .replace(/!/g, '%21')
        .replace(/'/g, '%27')
        .replace(/\(/g, '%28')
        .replace(/\)/g, '%29')
        .replace(/\*/g, '%2A');
}

/**
 * 创建 OAuth 签名
 * @param {string} oauthToken OAuth Token
 * @param {string} oauthTokenSecret OAuth Token Secret
 * @param {string} method HTTP 方法
 * @param {string} url 请求 URL
 * @param {string} body 请求体（可选）
 * @param {number} timestamp 时间戳（可选）
 * @param {string} nonce 随机字符串（可选）
 * @returns {Promise<Object>} 签名信息
 */
export async function createOAuthSignature(
    oauthToken,
    oauthTokenSecret,
    method = 'GET',
    url = '',
    body = '',
    timestamp = Math.floor(Date.now() / 1000),
    nonce = generateNonce()
) {
    if (!url) {
        throw new Error('URL is required');
    }

    method = method.toUpperCase();
    const parseUrl = new URL(url);
    const baseUrl = parseUrl.origin + parseUrl.pathname;
    
    // 收集参数
    const params = [];
    
    // URL 查询参数
    for (const [key, value] of parseUrl.searchParams.entries()) {
        params.push([key, value]);
    }
    
    // 请求体参数（仅当 Content-Type 为 application/x-www-form-urlencoded 时）
    if (body && typeof body === 'string' && !body.startsWith('{')) {
        try {
            const bodyParams = new URLSearchParams(body);
            for (const [key, value] of bodyParams.entries()) {
                params.push([key, value]);
            }
        } catch (e) {
            // 忽略解析错误
        }
    }
    
    // OAuth 参数
    params.push(['oauth_version', '1.0']);
    params.push(['oauth_signature_method', 'HMAC-SHA1']);
    params.push(['oauth_consumer_key', TWITTER_CONSUMER_KEY]);
    params.push(['oauth_token', oauthToken]);
    params.push(['oauth_nonce', nonce]);
    params.push(['oauth_timestamp', String(timestamp)]);
    
    // 参数排序
    params.sort((a, b) => {
        if (a[0] === b[0]) {
            return a[1] < b[1] ? -1 : a[1] > b[1] ? 1 : 0;
        }
        return a[0] < b[0] ? -1 : 1;
    });
    
    // 构建参数字符串
    const paramString = params
        .map(([key, value]) => `${oauthEncode(key)}=${oauthEncode(value)}`)
        .join('&');
    
    // 构建签名基础字符串
    const signatureBaseString = [
        method,
        oauthEncode(baseUrl),
        oauthEncode(paramString)
    ].join('&');
    
    // 构建签名密钥
    const signingKey = [
        oauthEncode(TWITTER_CONSUMER_SECRET),
        oauthEncode(oauthTokenSecret || '')
    ].join('&');
    
    // 计算签名
    const signature = crypto
        .createHmac('sha1', signingKey)
        .update(signatureBaseString)
        .digest('base64');
    
    // 构建 Authorization 头
    const authParams = [
        ['oauth_version', '1.0'],
        ['oauth_signature_method', 'HMAC-SHA1'],
        ['oauth_consumer_key', TWITTER_CONSUMER_KEY],
        ['oauth_token', oauthToken],
        ['oauth_nonce', nonce],
        ['oauth_timestamp', String(timestamp)],
        ['oauth_signature', signature]
    ];
    
    const authorization = 'OAuth ' + authParams
        .map(([key, value]) => `${key}="${oauthEncode(value)}"`)
        .join(', ');
    
    return {
        method,
        url,
        timestamp,
        nonce,
        signature,
        authorization,
        signatureBaseString,
        signingKey,
        params: Object.fromEntries(params)
    };
}

/**
 * 使用账号发送请求
 * @param {Object} account 账号信息
 * @param {string} method HTTP 方法
 * @param {string} url 请求 URL
 * @param {Object} options 请求选项
 * @returns {Promise<Response>} 响应
 */
export async function makeAuthenticatedRequest(account, method, url, options = {}) {
    const { oauth_token, oauth_token_secret } = account;
    
    const oauthData = await createOAuthSignature(
        oauth_token,
        oauth_token_secret,
        method,
        url,
        options.body
    );
    
    const headers = {
        'Authorization': oauthData.authorization,
        'User-Agent': 'TwitterAndroid/10.21.0-release.0',
        'X-Twitter-API-Version': '5',
        'X-Twitter-Client': 'TwitterAndroid',
        'X-Twitter-Client-Version': '10.21.0-release.0',
        'X-Twitter-Active-User': 'yes',
        ...options.headers
    };
    
    return fetch(url, {
        method,
        headers,
        ...options
    });
}

/**
 * 验证账号是否有效
 * @param {Object} account 账号信息
 * @returns {Promise<boolean>} 是否有效
 */
export async function validateAccount(account) {
    try {
        const response = await makeAuthenticatedRequest(
            account,
            'GET',
            'https://api.twitter.com/1.1/account/verify_credentials.json'
        );
        
        return response.ok;
    } catch (error) {
        console.error('账号验证失败:', error.message);
        return false;
    }
}

/**
 * 获取用户信息
 * @param {Object} account 账号信息
 * @param {string} userId 用户ID或用户名
 * @returns {Promise<Object>} 用户信息
 */
export async function getUserInfo(account, userId) {
    const url = `https://api.twitter.com/1.1/users/show.json?${new URLSearchParams({
        user_id: userId
    })}`;
    
    const response = await makeAuthenticatedRequest(account, 'GET', url);
    
    if (!response.ok) {
        throw new Error(`获取用户信息失败: ${response.status} ${response.statusText}`);
    }
    
    return response.json();
}

/**
 * 搜索推文
 * @param {Object} account 账号信息
 * @param {string} query 搜索查询
 * @param {Object} options 搜索选项
 * @returns {Promise<Object>} 搜索结果
 */
export async function searchTweets(account, query, options = {}) {
    const params = new URLSearchParams({
        q: query,
        result_type: options.result_type || 'recent',
        count: options.count || 20,
        ...options
    });
    
    const url = `https://api.twitter.com/1.1/search/tweets.json?${params}`;
    
    const response = await makeAuthenticatedRequest(account, 'GET', url);
    
    if (!response.ok) {
        throw new Error(`搜索推文失败: ${response.status} ${response.statusText}`);
    }
    
    return response.json();
}

/**
 * 获取用户时间线
 * @param {Object} account 账号信息
 * @param {string} userId 用户ID
 * @param {Object} options 选项
 * @returns {Promise<Object>} 时间线数据
 */
export async function getUserTimeline(account, userId, options = {}) {
    const params = new URLSearchParams({
        user_id: userId,
        count: options.count || 20,
        include_rts: options.include_rts || 'true',
        ...options
    });
    
    const url = `https://api.twitter.com/1.1/statuses/user_timeline.json?${params}`;
    
    const response = await makeAuthenticatedRequest(account, 'GET', url);
    
    if (!response.ok) {
        throw new Error(`获取用户时间线失败: ${response.status} ${response.statusText}`);
    }
    
    return response.json();
}

// 导出常量
export { TWITTER_CONSUMER_KEY, TWITTER_CONSUMER_SECRET };
