# 推特批量注册脚本

基于 Twitter Android 客户端 API 的批量账号注册脚本，可以创建大量的临时访客账号用于数据抓取。

## 功能特点

- 🚀 **高效批量注册**: 支持并发注册，可快速生成大量账号
- 🔄 **自动重试机制**: 内置智能重试，提高成功率
- 📊 **实时进度显示**: 显示注册进度、成功率和预计完成时间
- 💾 **自动保存**: 实时保存已生成的账号，支持断点续传
- 🛡️ **错误处理**: 完善的错误处理和日志记录
- 🌐 **代理支持**: 支持 HTTP/SOCKS5 代理
- 📁 **多格式输出**: 支持 JSON 和简单文本格式输出

## 技术原理

该脚本基于以下技术文档：
- [怎么爬 Twitter（Android）](https://blog.nest.moe/posts/how-to-crawl-twitter-with-android)
- [轻松创建一万个 Twitter 账号](https://diygod.cc/10k-twitter-accounts)

通过模拟 Twitter Android 客户端的注册流程，创建临时访客账号。这些账号具有以下特点：
- 权限较低但可访问大部分公开内容
- 有一定的频率限制
- 理论有效期约一个月
- 无法通过用户名或ID搜索

## 安装要求

- Node.js 18+ 或 Deno 或 Bun
- 稳定的网络连接
- （可选）代理服务

## 使用方法

### 1. 基本使用

```bash
# 使用 Node.js
node 推特批量注册.mjs

# 使用 Deno
deno run --allow-net --allow-read --allow-write 推特批量注册.mjs

# 使用 Bun
bun run 推特批量注册.mjs
```

### 2. 配置参数

编辑脚本中的 `config` 对象或使用 `config.json` 文件：

```javascript
const config = {
    // 并发数量 - 建议 3-5，过高可能被限制
    concurrency: 3,
    
    // 代理配置
    proxy: {
        enabled: false,
        url: '*************************************:port',
        type: 'http' // 'http' 或 'socks5'
    },
    
    // 请求超时时间 (毫秒)
    timeout: 30000,
    
    // 重试次数
    maxRetries: 3,
    
    // 请求间隔 (毫秒)
    requestDelay: 2000,
    
    // 目标账号数量
    targetAccountCount: 100,
    
    // 输出文件路径
    outputFile: './twitter_accounts.json',
    
    // 日志文件路径
    logFile: './registration.log'
};
```

### 3. 使用代理

如果需要使用代理服务（推荐用于大量注册）：

```javascript
proxy: {
    enabled: true,
    url: '*************************************:port',
    type: 'http'
}
```

推荐的代理服务：
- [proxy-cheap](https://app.proxy-cheap.com) - 性价比较高
- 其他住宅代理服务

## 输出格式

### JSON 格式 (twitter_accounts.json)

```json
{
  "generated_at": "2023-12-15T10:30:00.000Z",
  "total_count": 100,
  "accounts": [
    {
      "oauth_token": "**********-abcdefghijklmnopqrstuvwxyz",
      "oauth_token_secret": "abcdefghijklmnopqrstuvwxyz**********",
      "user_id": "**********123456789",
      "screen_name": "_LO_15122W00ABC",
      "name": "Open App User",
      "created_at": "2023-12-15T10:30:00.000Z",
      "guest_token": "**********abcdef"
    }
  ]
}
```

### 简单格式 (twitter_accounts_simple.txt)

```
TWITTER_OAUTH_TOKEN=token1,token2,token3
TWITTER_OAUTH_TOKEN_SECRET=secret1,secret2,secret3
TWITTER_SCREEN_NAMES=name1,name2,name3
```

## 使用生成的账号

生成的账号可以用于：

1. **OAuth 1.0a 签名请求**
2. **访问 Twitter GraphQL API**
3. **数据抓取和监控**

### 示例：使用账号进行 API 请求

```javascript
import { createOAuthSignature } from './oauth-utils.js';

const account = accounts[0];
const signature = await createOAuthSignature(
    account.oauth_token,
    account.oauth_token_secret,
    'GET',
    'https://api.twitter.com/graphql/...'
);

const response = await fetch(url, {
    headers: {
        'Authorization': signature.authorization
    }
});
```

## 注意事项

### 限制和风控

- **IP 限制**: 每个 IP 在短时间内只能生成有限数量的账号
- **频率限制**: 不要设置过高的并发数，建议 3-5
- **账号有效期**: 临时账号有效期约一个月
- **使用限制**: 账号有严格的请求频率限制

### 最佳实践

1. **使用代理**: 大量注册时建议使用代理池
2. **分批注册**: 避免一次性注册过多账号
3. **保存备份**: 及时保存生成的账号信息
4. **监控日志**: 关注错误日志，调整策略
5. **遵守规则**: 合理使用，避免滥用

### 错误处理

常见错误及解决方案：

- **Rate limit exceeded**: IP 被限制，更换 IP 或等待
- **Invalid or expired token**: 账号过期，重新生成
- **Could not authenticate you**: 签名错误，检查参数
- **Network timeout**: 网络问题，检查连接或代理

## 法律声明

本脚本仅供学习和研究使用，请遵守相关法律法规和 Twitter 服务条款。使用者需自行承担使用风险。

## 参考资料

- [Twitter OAuth 1.0a 文档](https://developer.twitter.com/en/docs/authentication/oauth-1-0a)
- [Twitter API 错误代码](https://developer.twitter.com/en/support/twitter-api/error-troubleshooting)
- [Nitter Guest Account 实现](https://github.com/zedeus/nitter/wiki/Guest-Account-Branch-Deployment)

## 更新日志

- v1.0.0: 初始版本，支持基本的批量注册功能
- 支持并发注册、自动重试、进度显示
- 支持代理、多格式输出、断点续传

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个脚本。

## 许可证

MIT License
