/**
 * 概念设计：单个MCP控制多个浏览器
 * 通过窗口编号识别和控制不同的浏览器实例
 */

interface BrowserConnection {
  browserId: string;
  port: number;
  httpClient: any; // StreamableHTTPClientTransport
  isConnected: boolean;
  lastSeen: number;
}

interface MultiBrowserToolCall {
  toolName: string;
  browserId?: string; // 可选，如果不指定则使用默认浏览器
  windowId?: number; // 可选，指定特定窗口
  args: any;
}

class MultiBrowserMCPServer {
  private browserConnections: Map<string, BrowserConnection> = new Map();
  private defaultBrowserId: string | null = null;

  /**
   * 注册新的浏览器连接
   */
  async registerBrowser(browserId: string, port: number): Promise<void> {
    try {
      const httpClient = new StreamableHTTPClientTransport(
        new URL(`http://127.0.0.1:${port}/mcp`), 
        {}
      );
      
      // 测试连接
      await httpClient.connect();
      
      const connection: BrowserConnection = {
        browserId,
        port,
        httpClient,
        isConnected: true,
        lastSeen: Date.now()
      };
      
      this.browserConnections.set(browserId, connection);
      
      // 如果是第一个浏览器，设为默认
      if (!this.defaultBrowserId) {
        this.defaultBrowserId = browserId;
      }
      
      console.log(`Browser ${browserId} registered on port ${port}`);
    } catch (error) {
      console.error(`Failed to register browser ${browserId}:`, error);
    }
  }

  /**
   * 获取所有浏览器的窗口和标签页信息
   */
  async getAllBrowsersInfo(): Promise<any> {
    const allBrowsersInfo = [];
    
    for (const [browserId, connection] of this.browserConnections) {
      if (!connection.isConnected) continue;
      
      try {
        const result = await connection.httpClient.callTool({
          name: 'get_windows_and_tabs',
          arguments: {}
        });
        
        // 为每个窗口添加浏览器ID标识
        const browserInfo = {
          browserId,
          port: connection.port,
          ...result,
          windows: result.windows?.map((window: any) => ({
            ...window,
            browserId, // 添加浏览器标识
            globalWindowId: `${browserId}_${window.windowId}` // 全局唯一窗口ID
          }))
        };
        
        allBrowsersInfo.push(browserInfo);
      } catch (error) {
        console.error(`Failed to get info from browser ${browserId}:`, error);
        connection.isConnected = false;
      }
    }
    
    return {
      totalBrowsers: allBrowsersInfo.length,
      browsers: allBrowsersInfo
    };
  }

  /**
   * 根据窗口ID路由工具调用到正确的浏览器
   */
  async routeToolCall(call: MultiBrowserToolCall): Promise<any> {
    let targetBrowser: BrowserConnection | null = null;
    
    // 1. 如果指定了browserId，直接使用
    if (call.browserId) {
      targetBrowser = this.browserConnections.get(call.browserId) || null;
    }
    // 2. 如果指定了windowId，查找包含该窗口的浏览器
    else if (call.windowId) {
      targetBrowser = await this.findBrowserByWindowId(call.windowId);
    }
    // 3. 使用默认浏览器
    else if (this.defaultBrowserId) {
      targetBrowser = this.browserConnections.get(this.defaultBrowserId) || null;
    }
    
    if (!targetBrowser || !targetBrowser.isConnected) {
      throw new Error('No available browser connection found');
    }
    
    // 执行工具调用
    try {
      const result = await targetBrowser.httpClient.callTool({
        name: call.toolName,
        arguments: call.args
      });
      
      return {
        ...result,
        executedOn: {
          browserId: targetBrowser.browserId,
          port: targetBrowser.port
        }
      };
    } catch (error) {
      console.error(`Tool call failed on browser ${targetBrowser.browserId}:`, error);
      throw error;
    }
  }

  /**
   * 根据窗口ID查找对应的浏览器
   */
  private async findBrowserByWindowId(windowId: number): Promise<BrowserConnection | null> {
    for (const [browserId, connection] of this.browserConnections) {
      if (!connection.isConnected) continue;
      
      try {
        const result = await connection.httpClient.callTool({
          name: 'get_windows_and_tabs',
          arguments: {}
        });
        
        const hasWindow = result.windows?.some((window: any) => window.windowId === windowId);
        if (hasWindow) {
          return connection;
        }
      } catch (error) {
        console.error(`Error checking windows in browser ${browserId}:`, error);
      }
    }
    
    return null;
  }

  /**
   * 创建增强的工具模式，支持浏览器选择
   */
  createEnhancedToolSchemas(): any[] {
    const baseTools = [
      {
        name: 'get_all_browsers_info',
        description: 'Get information about all connected browsers and their windows/tabs',
        inputSchema: {
          type: 'object',
          properties: {},
          required: []
        }
      },
      {
        name: 'navigate_browser',
        description: 'Navigate to URL in a specific browser or window',
        inputSchema: {
          type: 'object',
          properties: {
            url: { type: 'string', description: 'URL to navigate to' },
            browserId: { type: 'string', description: 'Browser ID (optional)' },
            windowId: { type: 'number', description: 'Window ID (optional)' },
            newWindow: { type: 'boolean', description: 'Open in new window' }
          },
          required: ['url']
        }
      },
      {
        name: 'screenshot_browser',
        description: 'Take screenshot from a specific browser or window',
        inputSchema: {
          type: 'object',
          properties: {
            browserId: { type: 'string', description: 'Browser ID (optional)' },
            windowId: { type: 'number', description: 'Window ID (optional)' },
            fullPage: { type: 'boolean', description: 'Full page screenshot' }
          },
          required: []
        }
      }
    ];
    
    return baseTools;
  }
}

// 使用示例
const multiBrowserServer = new MultiBrowserMCPServer();

// 注册多个浏览器
await multiBrowserServer.registerBrowser('browser1', 12306);
await multiBrowserServer.registerBrowser('browser2', 12307);
await multiBrowserServer.registerBrowser('browser3', 12308);

// 获取所有浏览器信息
const allInfo = await multiBrowserServer.getAllBrowsersInfo();

// 在特定浏览器中导航
await multiBrowserServer.routeToolCall({
  toolName: 'chrome_navigate',
  browserId: 'browser2',
  args: { url: 'https://github.com' }
});

// 在特定窗口中截图
await multiBrowserServer.routeToolCall({
  toolName: 'chrome_screenshot',
  windowId: 999832583,
  args: { fullPage: true }
});

export { MultiBrowserMCPServer, MultiBrowserToolCall, BrowserConnection };
