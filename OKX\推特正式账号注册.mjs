// 推特正式账号批量注册脚本
// 创建可以绑定第三方平台的正式 Twitter 账号
// 需要邮箱服务和接码平台支持

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 配置参数
const config = {
    // 并发数量 - 正式注册建议更保守
    concurrency: 2,
    
    // 代理配置 - 正式注册强烈建议使用高质量代理
    proxy: {
        enabled: true,
        url: '', // 住宅代理地址
        type: 'http'
    },
    
    // 邮箱服务配置
    emailService: {
        provider: 'temp-mail', // 'temp-mail', 'guerrilla-mail', 'custom'
        apiKey: '', // 如果需要
        domain: '@tempmail.com' // 邮箱域名
    },
    
    // 接码平台配置
    smsService: {
        provider: 'sms-activate', // 'sms-activate', '5sim', 'custom'
        apiKey: '', // 接码平台 API Key
        country: 'us', // 国家代码
        service: 'tw' // Twitter 服务代码
    },
    
    // 验证码解决服务
    captchaService: {
        provider: '2captcha', // '2captcha', 'anticaptcha', 'custom'
        apiKey: '', // 验证码服务 API Key
    },
    
    // 账号信息生成
    accountGeneration: {
        nameSource: 'random', // 'random', 'file', 'api'
        nameFile: './names.txt', // 姓名列表文件
        passwordLength: 12,
        bioSource: 'random', // 个人简介来源
        avatarSource: 'none' // 头像来源
    },
    
    // 请求配置
    timeout: 60000, // 正式注册需要更长时间
    maxRetries: 5,
    requestDelay: 5000, // 更长的请求间隔
    
    // 目标数量
    targetAccountCount: 10,
    
    // 输出配置
    outputFile: path.join(__dirname, 'real_twitter_accounts.json'),
    logFile: path.join(__dirname, 'real_registration.log')
};

// Twitter API 配置
const TWITTER_CONFIG = {
    // 网页版 Bearer Token (用于正式注册)
    bearerToken: 'Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA',
    
    baseURL: 'https://api.twitter.com/1.1/',
    
    // 网页版请求头
    headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Content-Type': 'application/json',
        'Origin': 'https://twitter.com',
        'Referer': 'https://twitter.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site'
    }
};

// 统计信息
const stats = {
    total: 0,
    success: 0,
    failed: 0,
    emailVerificationFailed: 0,
    smsVerificationFailed: 0,
    captchaFailed: 0,
    startTime: Date.now()
};

// 存储生成的账号
const accounts = [];

// 日志函数
function log(message, level = 'INFO') {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level}] ${message}`;
    console.log(logMessage);
    
    fs.appendFileSync(config.logFile, logMessage + '\n');
}

// 延迟函数
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 生成随机字符串
function generateRandomString(length, chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********') {
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// 生成随机姓名
function generateRandomName() {
    const firstNames = ['Alex', 'Jordan', 'Taylor', 'Casey', 'Morgan', 'Riley', 'Avery', 'Quinn', 'Sage', 'River'];
    const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez'];
    
    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
    const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
    
    return `${firstName} ${lastName}`;
}

// 生成用户名
function generateUsername(name) {
    const cleanName = name.toLowerCase().replace(/\s+/g, '');
    const randomSuffix = generateRandomString(4, '**********');
    return `${cleanName}${randomSuffix}`;
}

// 生成密码
function generatePassword() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********!@#$%^&*';
    return generateRandomString(config.accountGeneration.passwordLength, chars);
}

// HTTP 请求函数
async function makeRequest(url, options = {}) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), config.timeout);
    
    try {
        const response = await fetch(url, {
            ...options,
            signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        const responseData = {
            status: response.status,
            statusText: response.statusText,
            headers: Object.fromEntries(response.headers.entries()),
            cookies: response.headers.get('set-cookie') || '',
            ok: response.ok
        };
        
        try {
            responseData.data = await response.json();
        } catch (e) {
            responseData.data = await response.text();
        }
        
        return responseData;
    } catch (error) {
        clearTimeout(timeoutId);
        throw error;
    }
}

// 获取临时邮箱
async function getTempEmail() {
    log('获取临时邮箱...');
    
    try {
        // 这里需要根据你选择的邮箱服务实现
        // 示例：使用 temp-mail.org API
        const response = await makeRequest('https://api.temp-mail.org/request/mail/id/1/format/json');
        
        if (response.ok && response.data) {
            const email = response.data;
            log(`获取到邮箱: ${email}`);
            return email;
        }
        
        throw new Error('获取邮箱失败');
    } catch (error) {
        log(`获取邮箱失败: ${error.message}`, 'ERROR');
        throw error;
    }
}

// 获取邮箱验证码
async function getEmailVerificationCode(email, timeout = 60000) {
    log(`等待邮箱验证码: ${email}`);
    
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
        try {
            // 这里需要根据你的邮箱服务实现
            // 示例：检查邮箱收件箱
            const response = await makeRequest(`https://api.temp-mail.org/request/mail/id/${email}/format/json`);
            
            if (response.ok && response.data && response.data.length > 0) {
                // 解析验证码
                const latestEmail = response.data[0];
                const codeMatch = latestEmail.mail_text.match(/(\d{6})/);
                
                if (codeMatch) {
                    const code = codeMatch[1];
                    log(`获取到邮箱验证码: ${code}`);
                    return code;
                }
            }
            
            await delay(5000); // 等待5秒后重试
        } catch (error) {
            log(`检查邮箱失败: ${error.message}`, 'WARN');
            await delay(5000);
        }
    }
    
    throw new Error('邮箱验证码获取超时');
}

// 获取手机号
async function getTempPhone() {
    log('获取临时手机号...');
    
    try {
        // 这里需要根据你选择的接码平台实现
        // 示例：使用 sms-activate.org API
        const response = await makeRequest(`https://sms-activate.org/stubs/handler_api.php?api_key=${config.smsService.apiKey}&action=getNumber&service=${config.smsService.service}&country=${config.smsService.country}`);
        
        if (response.ok && response.data.startsWith('ACCESS_NUMBER')) {
            const parts = response.data.split(':');
            const activationId = parts[1];
            const phoneNumber = parts[2];
            
            log(`获取到手机号: ${phoneNumber} (ID: ${activationId})`);
            return { phone: phoneNumber, activationId };
        }
        
        throw new Error('获取手机号失败');
    } catch (error) {
        log(`获取手机号失败: ${error.message}`, 'ERROR');
        throw error;
    }
}

// 获取短信验证码
async function getSMSVerificationCode(activationId, timeout = 60000) {
    log(`等待短信验证码: ${activationId}`);
    
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
        try {
            const response = await makeRequest(`https://sms-activate.org/stubs/handler_api.php?api_key=${config.smsService.apiKey}&action=getStatus&id=${activationId}`);
            
            if (response.ok) {
                if (response.data.startsWith('STATUS_OK')) {
                    const code = response.data.split(':')[1];
                    log(`获取到短信验证码: ${code}`);
                    return code;
                }
                
                if (response.data === 'STATUS_WAIT_CODE') {
                    await delay(10000); // 等待10秒后重试
                    continue;
                }
            }
            
            await delay(10000);
        } catch (error) {
            log(`检查短信失败: ${error.message}`, 'WARN');
            await delay(10000);
        }
    }
    
    throw new Error('短信验证码获取超时');
}

// 解决验证码
async function solveCaptcha(siteKey, pageUrl) {
    log('解决人机验证...');
    
    try {
        // 这里需要根据你选择的验证码服务实现
        // 示例：使用 2captcha API
        
        // 1. 提交验证码任务
        const submitResponse = await makeRequest('https://2captcha.com/in.php', {
            method: 'POST',
            body: new URLSearchParams({
                key: config.captchaService.apiKey,
                method: 'userrecaptcha',
                googlekey: siteKey,
                pageurl: pageUrl
            })
        });
        
        if (!submitResponse.ok || !submitResponse.data.startsWith('OK|')) {
            throw new Error('验证码任务提交失败');
        }
        
        const taskId = submitResponse.data.split('|')[1];
        log(`验证码任务ID: ${taskId}`);
        
        // 2. 等待验证码解决
        await delay(20000); // 等待20秒
        
        for (let i = 0; i < 30; i++) {
            const resultResponse = await makeRequest(`https://2captcha.com/res.php?key=${config.captchaService.apiKey}&action=get&id=${taskId}`);
            
            if (resultResponse.ok) {
                if (resultResponse.data.startsWith('OK|')) {
                    const solution = resultResponse.data.split('|')[1];
                    log('验证码解决成功');
                    return solution;
                }
                
                if (resultResponse.data === 'CAPCHA_NOT_READY') {
                    await delay(5000);
                    continue;
                }
            }
            
            await delay(5000);
        }
        
        throw new Error('验证码解决超时');
    } catch (error) {
        log(`验证码解决失败: ${error.message}`, 'ERROR');
        throw error;
    }
}

// 开始注册流程
async function startSignupFlow() {
    log('开始注册流程...');

    try {
        // 1. 获取 guest token
        const guestResponse = await makeRequest(`${TWITTER_CONFIG.baseURL}guest/activate.json`, {
            method: 'POST',
            headers: {
                'Authorization': TWITTER_CONFIG.bearerToken
            }
        });

        if (!guestResponse.ok) {
            throw new Error('获取 guest token 失败');
        }

        const guestToken = guestResponse.data.guest_token;
        log(`获取到 guest token: ${guestToken.substring(0, 20)}...`);

        // 2. 开始注册流程
        const signupResponse = await makeRequest(`${TWITTER_CONFIG.baseURL}onboarding/task.json?flow_name=signup`, {
            method: 'POST',
            headers: {
                ...TWITTER_CONFIG.headers,
                'Authorization': TWITTER_CONFIG.bearerToken,
                'X-Guest-Token': guestToken
            },
            body: JSON.stringify({
                flow_token: null,
                input_flow_data: {
                    flow_context: {
                        debug_overrides: {},
                        start_location: {
                            location: 'manual_link'
                        }
                    }
                },
                subtask_versions: {
                    action_list: 2,
                    alert_dialog: 1,
                    app_download_cta: 1,
                    check_logged_in_account: 1,
                    choice_selection: 3,
                    contacts_live_sync_permission_prompt: 0,
                    cta: 7,
                    email_verification: 2,
                    end_flow: 1,
                    enter_date: 1,
                    enter_email: 2,
                    enter_password: 5,
                    enter_phone: 2,
                    enter_recaptcha: 1,
                    enter_text: 5,
                    enter_username: 2,
                    generic_urt: 3,
                    in_app_notification: 1,
                    interest_picker: 3,
                    js_instrumentation: 1,
                    menu_dialog: 1,
                    notifications_permission_prompt: 2,
                    open_account: 2,
                    open_home_timeline: 1,
                    open_link: 1,
                    phone_verification: 4,
                    privacy_options: 1,
                    security_key: 3,
                    select_avatar: 4,
                    select_banner: 2,
                    settings_list: 7,
                    show_code: 1,
                    sign_up: 2,
                    sign_up_review: 4,
                    tweet_selection_urt: 1,
                    update_users: 1,
                    upload_media: 1,
                    user_recommendations_list: 4,
                    user_recommendations_urt: 1,
                    wait_spinner: 3,
                    web_modal: 1
                }
            })
        });

        if (!signupResponse.ok) {
            throw new Error('注册流程启动失败');
        }

        return {
            guestToken,
            flowToken: signupResponse.data.flow_token,
            cookies: signupResponse.cookies
        };

    } catch (error) {
        log(`注册流程启动失败: ${error.message}`, 'ERROR');
        throw error;
    }
}

// 提交邮箱信息
async function submitEmail(guestToken, flowToken, email) {
    log(`提交邮箱: ${email}`);

    try {
        const response = await makeRequest(`${TWITTER_CONFIG.baseURL}onboarding/task.json`, {
            method: 'POST',
            headers: {
                ...TWITTER_CONFIG.headers,
                'Authorization': TWITTER_CONFIG.bearerToken,
                'X-Guest-Token': guestToken
            },
            body: JSON.stringify({
                flow_token: flowToken,
                subtask_inputs: [{
                    subtask_id: 'SignupEnterEmail',
                    enter_email: {
                        email: email,
                        link: 'next_link'
                    }
                }]
            })
        });

        if (!response.ok) {
            throw new Error('邮箱提交失败');
        }

        log('邮箱提交成功');
        return response.data.flow_token;

    } catch (error) {
        log(`邮箱提交失败: ${error.message}`, 'ERROR');
        throw error;
    }
}

// 提交个人信息
async function submitPersonalInfo(guestToken, flowToken, name, username, password) {
    log(`提交个人信息: ${name} (@${username})`);

    try {
        // 提交姓名
        const nameResponse = await makeRequest(`${TWITTER_CONFIG.baseURL}onboarding/task.json`, {
            method: 'POST',
            headers: {
                ...TWITTER_CONFIG.headers,
                'Authorization': TWITTER_CONFIG.bearerToken,
                'X-Guest-Token': guestToken
            },
            body: JSON.stringify({
                flow_token: flowToken,
                subtask_inputs: [{
                    subtask_id: 'SignupEnterName',
                    enter_text: {
                        text: name,
                        link: 'next_link'
                    }
                }]
            })
        });

        if (!nameResponse.ok) {
            throw new Error('姓名提交失败');
        }

        // 提交用户名
        const usernameResponse = await makeRequest(`${TWITTER_CONFIG.baseURL}onboarding/task.json`, {
            method: 'POST',
            headers: {
                ...TWITTER_CONFIG.headers,
                'Authorization': TWITTER_CONFIG.bearerToken,
                'X-Guest-Token': guestToken
            },
            body: JSON.stringify({
                flow_token: nameResponse.data.flow_token,
                subtask_inputs: [{
                    subtask_id: 'SignupEnterUsername',
                    enter_text: {
                        text: username,
                        link: 'next_link'
                    }
                }]
            })
        });

        if (!usernameResponse.ok) {
            throw new Error('用户名提交失败');
        }

        // 提交密码
        const passwordResponse = await makeRequest(`${TWITTER_CONFIG.baseURL}onboarding/task.json`, {
            method: 'POST',
            headers: {
                ...TWITTER_CONFIG.headers,
                'Authorization': TWITTER_CONFIG.bearerToken,
                'X-Guest-Token': guestToken
            },
            body: JSON.stringify({
                flow_token: usernameResponse.data.flow_token,
                subtask_inputs: [{
                    subtask_id: 'SignupEnterPassword',
                    enter_password: {
                        password: password,
                        link: 'next_link'
                    }
                }]
            })
        });

        if (!passwordResponse.ok) {
            throw new Error('密码提交失败');
        }

        log('个人信息提交成功');
        return passwordResponse.data.flow_token;

    } catch (error) {
        log(`个人信息提交失败: ${error.message}`, 'ERROR');
        throw error;
    }
}

// 处理邮箱验证
async function handleEmailVerification(guestToken, flowToken, email) {
    log('处理邮箱验证...');

    try {
        // 获取邮箱验证码
        const verificationCode = await getEmailVerificationCode(email);

        // 提交验证码
        const response = await makeRequest(`${TWITTER_CONFIG.baseURL}onboarding/task.json`, {
            method: 'POST',
            headers: {
                ...TWITTER_CONFIG.headers,
                'Authorization': TWITTER_CONFIG.bearerToken,
                'X-Guest-Token': guestToken
            },
            body: JSON.stringify({
                flow_token: flowToken,
                subtask_inputs: [{
                    subtask_id: 'SignupEmailVerification',
                    enter_text: {
                        text: verificationCode,
                        link: 'next_link'
                    }
                }]
            })
        });

        if (!response.ok) {
            throw new Error('邮箱验证失败');
        }

        log('邮箱验证成功');
        return response.data.flow_token;

    } catch (error) {
        stats.emailVerificationFailed++;
        log(`邮箱验证失败: ${error.message}`, 'ERROR');
        throw error;
    }
}

// 处理手机验证
async function handlePhoneVerification(guestToken, flowToken) {
    log('处理手机验证...');

    try {
        // 获取临时手机号
        const phoneData = await getTempPhone();

        // 提交手机号
        const phoneResponse = await makeRequest(`${TWITTER_CONFIG.baseURL}onboarding/task.json`, {
            method: 'POST',
            headers: {
                ...TWITTER_CONFIG.headers,
                'Authorization': TWITTER_CONFIG.bearerToken,
                'X-Guest-Token': guestToken
            },
            body: JSON.stringify({
                flow_token: flowToken,
                subtask_inputs: [{
                    subtask_id: 'SignupEnterPhone',
                    enter_phone: {
                        phone_number: phoneData.phone,
                        link: 'next_link'
                    }
                }]
            })
        });

        if (!phoneResponse.ok) {
            throw new Error('手机号提交失败');
        }

        // 获取短信验证码
        const smsCode = await getSMSVerificationCode(phoneData.activationId);

        // 提交短信验证码
        const smsResponse = await makeRequest(`${TWITTER_CONFIG.baseURL}onboarding/task.json`, {
            method: 'POST',
            headers: {
                ...TWITTER_CONFIG.headers,
                'Authorization': TWITTER_CONFIG.bearerToken,
                'X-Guest-Token': guestToken
            },
            body: JSON.stringify({
                flow_token: phoneResponse.data.flow_token,
                subtask_inputs: [{
                    subtask_id: 'SignupPhoneVerification',
                    enter_text: {
                        text: smsCode,
                        link: 'next_link'
                    }
                }]
            })
        });

        if (!smsResponse.ok) {
            throw new Error('短信验证失败');
        }

        log('手机验证成功');
        return {
            flowToken: smsResponse.data.flow_token,
            phone: phoneData.phone
        };

    } catch (error) {
        stats.smsVerificationFailed++;
        log(`手机验证失败: ${error.message}`, 'ERROR');
        throw error;
    }
}

// 处理人机验证
async function handleCaptchaVerification(guestToken, flowToken) {
    log('处理人机验证...');

    try {
        // 这里需要根据实际的验证码类型处理
        // Twitter 可能使用 reCAPTCHA 或其他验证方式

        const siteKey = '6LdqFQATAAAAAOBhKlDulub_R2y4j_Wvs6_eSNKB'; // Twitter 的 reCAPTCHA site key
        const pageUrl = 'https://twitter.com/i/flow/signup';

        const captchaSolution = await solveCaptcha(siteKey, pageUrl);

        const response = await makeRequest(`${TWITTER_CONFIG.baseURL}onboarding/task.json`, {
            method: 'POST',
            headers: {
                ...TWITTER_CONFIG.headers,
                'Authorization': TWITTER_CONFIG.bearerToken,
                'X-Guest-Token': guestToken
            },
            body: JSON.stringify({
                flow_token: flowToken,
                subtask_inputs: [{
                    subtask_id: 'SignupRecaptcha',
                    enter_recaptcha: {
                        recaptcha_response: captchaSolution,
                        link: 'next_link'
                    }
                }]
            })
        });

        if (!response.ok) {
            throw new Error('人机验证失败');
        }

        log('人机验证成功');
        return response.data.flow_token;

    } catch (error) {
        stats.captchaFailed++;
        log(`人机验证失败: ${error.message}`, 'ERROR');
        throw error;
    }
}

// 完成注册
async function completeSignup(guestToken, flowToken) {
    log('完成注册...');

    try {
        const response = await makeRequest(`${TWITTER_CONFIG.baseURL}onboarding/task.json`, {
            method: 'POST',
            headers: {
                ...TWITTER_CONFIG.headers,
                'Authorization': TWITTER_CONFIG.bearerToken,
                'X-Guest-Token': guestToken
            },
            body: JSON.stringify({
                flow_token: flowToken,
                subtask_inputs: [{
                    subtask_id: 'SignupReview',
                    sign_up_review: {
                        link: 'next_link'
                    }
                }]
            })
        });

        if (!response.ok) {
            throw new Error('注册完成失败');
        }

        // 检查是否有用户信息返回
        const userData = response.data.subtasks?.find(task => task.subtask_id === 'CompleteSignup');

        if (userData) {
            log('注册完成成功');
            return userData;
        }

        throw new Error('注册完成但未获取到用户信息');

    } catch (error) {
        log(`注册完成失败: ${error.message}`, 'ERROR');
        throw error;
    }
}

// 生成单个正式账号
async function generateRealAccount() {
    let retries = 0;

    while (retries < config.maxRetries) {
        try {
            log(`开始生成正式账号 (第 ${retries + 1} 次尝试)`);

            // 生成账号信息
            const name = generateRandomName();
            const username = generateUsername(name);
            const password = generatePassword();

            log(`生成账号信息: ${name} (@${username})`);

            // 1. 获取临时邮箱
            const email = await getTempEmail();

            // 2. 开始注册流程
            const { guestToken, flowToken: initialFlowToken } = await startSignupFlow();

            // 3. 提交邮箱
            let flowToken = await submitEmail(guestToken, initialFlowToken, email);

            // 4. 处理邮箱验证
            flowToken = await handleEmailVerification(guestToken, flowToken, email);

            // 5. 提交个人信息
            flowToken = await submitPersonalInfo(guestToken, flowToken, name, username, password);

            // 6. 处理手机验证（如果需要）
            let phone = null;
            try {
                const phoneResult = await handlePhoneVerification(guestToken, flowToken);
                flowToken = phoneResult.flowToken;
                phone = phoneResult.phone;
            } catch (error) {
                log('手机验证跳过或失败，继续注册流程', 'WARN');
            }

            // 7. 处理人机验证（如果需要）
            try {
                flowToken = await handleCaptchaVerification(guestToken, flowToken);
            } catch (error) {
                log('人机验证跳过或失败，继续注册流程', 'WARN');
            }

            // 8. 完成注册
            const userData = await completeSignup(guestToken, flowToken);

            // 9. 构建账号信息
            const accountInfo = {
                email: email,
                password: password,
                name: name,
                username: username,
                phone: phone,
                user_id: userData.user?.id_str,
                screen_name: userData.user?.screen_name,
                created_at: new Date().toISOString(),
                guest_token: guestToken,
                account_type: 'real', // 标记为正式账号
                can_login: true, // 可以登录
                can_bind_platforms: true // 可以绑定第三方平台
            };

            log(`成功创建正式账号: @${accountInfo.screen_name} (${accountInfo.user_id})`);
            return accountInfo;

        } catch (error) {
            retries++;
            log(`账号生成失败: ${error.message}`, 'ERROR');

            if (retries < config.maxRetries) {
                log(`等待 ${config.requestDelay * retries}ms 后重试...`);
                await delay(config.requestDelay * retries);
            }
        }
    }

    return null;
}

// 批量生成正式账号
async function generateRealAccounts() {
    log(`开始批量生成正式推特账号，目标数量: ${config.targetAccountCount}`);
    log(`并发数: ${config.concurrency}`);

    const promises = [];
    let completed = 0;

    // 创建并发任务
    for (let i = 0; i < config.concurrency; i++) {
        const promise = (async () => {
            while (accounts.length < config.targetAccountCount) {
                const account = await generateRealAccount();

                if (account) {
                    accounts.push(account);
                    stats.success++;
                    log(`成功生成账号 ${stats.success}/${config.targetAccountCount}: @${account.screen_name}`);

                    // 保存到文件
                    saveAccounts();
                } else {
                    stats.failed++;
                    log(`账号生成失败，已失败 ${stats.failed} 次`, 'WARN');
                }

                stats.total++;
                completed++;

                // 显示进度
                if (completed % 5 === 0) {
                    showProgress();
                }

                // 请求间隔
                await delay(config.requestDelay);
            }
        })();

        promises.push(promise);
    }

    // 等待所有任务完成
    await Promise.all(promises);

    log(`批量生成完成！成功: ${stats.success}, 失败: ${stats.failed}`);
    showFinalStats();
}

// 显示进度
function showProgress() {
    const elapsed = Date.now() - stats.startTime;
    const rate = stats.total / (elapsed / 1000);
    const eta = (config.targetAccountCount - accounts.length) / rate;

    log(`进度: ${accounts.length}/${config.targetAccountCount} (${(accounts.length/config.targetAccountCount*100).toFixed(1)}%)`);
    log(`速率: ${rate.toFixed(2)} 账号/秒, 预计剩余时间: ${Math.round(eta)}秒`);
    log(`验证失败统计: 邮箱=${stats.emailVerificationFailed}, 短信=${stats.smsVerificationFailed}, 验证码=${stats.captchaFailed}`);
}

// 显示最终统计
function showFinalStats() {
    const elapsed = Date.now() - stats.startTime;
    const avgRate = stats.total / (elapsed / 1000);

    log('='.repeat(50));
    log('最终统计:');
    log(`总耗时: ${Math.round(elapsed / 1000)}秒`);
    log(`成功账号: ${stats.success}`);
    log(`失败次数: ${stats.failed}`);
    log(`成功率: ${(stats.success / stats.total * 100).toFixed(1)}%`);
    log(`平均速率: ${avgRate.toFixed(2)} 账号/秒`);
    log(`验证失败统计:`);
    log(`  - 邮箱验证失败: ${stats.emailVerificationFailed}`);
    log(`  - 短信验证失败: ${stats.smsVerificationFailed}`);
    log(`  - 人机验证失败: ${stats.captchaFailed}`);
    log(`账号保存位置: ${config.outputFile}`);
    log('='.repeat(50));
}

// 保存账号到文件
function saveAccounts() {
    try {
        const data = {
            generated_at: new Date().toISOString(),
            total_count: accounts.length,
            account_type: 'real_twitter_accounts',
            accounts: accounts
        };

        fs.writeFileSync(config.outputFile, JSON.stringify(data, null, 2));

        // 同时保存为登录格式
        const loginFormat = accounts.map(acc => ({
            email: acc.email,
            password: acc.password,
            username: acc.username,
            screen_name: acc.screen_name
        }));

        const loginFile = config.outputFile.replace('.json', '_login.json');
        fs.writeFileSync(loginFile, JSON.stringify(loginFormat, null, 2));

    } catch (error) {
        log(`保存账号失败: ${error.message}`, 'ERROR');
    }
}

// 主函数
async function main() {
    try {
        log('推特正式账号批量注册脚本启动');
        log(`配置: 目标数量=${config.targetAccountCount}, 并发数=${config.concurrency}`);

        // 检查必要的配置
        if (!config.emailService.provider) {
            throw new Error('请配置邮箱服务');
        }

        if (!config.smsService.apiKey) {
            log('警告: 未配置接码平台，可能无法完成手机验证', 'WARN');
        }

        if (!config.captchaService.apiKey) {
            log('警告: 未配置验证码服务，可能无法通过人机验证', 'WARN');
        }

        // 创建输出目录
        const outputDir = path.dirname(config.outputFile);
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        // 开始生成账号
        await generateRealAccounts();

        log('脚本执行完成');

    } catch (error) {
        log(`脚本执行失败: ${error.message}`, 'ERROR');
        process.exit(1);
    }
}

// 处理程序退出
process.on('SIGINT', () => {
    log('收到中断信号，正在保存已生成的账号...');
    saveAccounts();
    showFinalStats();
    process.exit(0);
});

// 启动脚本
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}
