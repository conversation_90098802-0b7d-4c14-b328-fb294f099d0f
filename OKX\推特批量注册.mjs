// 推特批量注册脚本
// 基于 Android 客户端 API 创建临时访客账号
// 参考: https://blog.nest.moe/posts/how-to-crawl-twitter-with-android
// 参考: https://diygod.cc/10k-twitter-accounts

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 配置参数
const config = {
    // 并发数量 - 不要设置太高，避免被Twitter发现
    concurrency: 3,
    
    // 代理配置 - 如果需要使用代理
    proxy: {
        enabled: false,
        url: '', // 例如: '*************************************:port'
        type: 'http' // 'http' 或 'socks5'
    },
    
    // 请求超时时间 (毫秒)
    timeout: 30000,
    
    // 重试次数
    maxRetries: 3,
    
    // 请求间隔 (毫秒)
    requestDelay: 1000,
    
    // 目标账号数量
    targetAccountCount: 100,
    
    // 输出文件路径
    outputFile: path.join(__dirname, 'twitter_accounts.json'),
    
    // 日志文件路径
    logFile: path.join(__dirname, 'registration.log')
};

// Twitter API 配置
const TWITTER_CONFIG = {
    // Android 客户端的 Bearer Token
    bearerToken: 'Bearer AAAAAAAAAAAAAAAAAAAAAFXzAwAAAAAAMHCxpeSDG1gLNLghVe8d74hl6k4%3DRUMF4xAQLsbeBhTSRrCiQpJtxoGWeyHrDb5te2jpGskWDFW82F',
    
    // API 基础 URL
    baseURL: 'https://api.twitter.com/1.1/',
    
    // 请求头
    headers: {
        'User-Agent': 'TwitterAndroid/10.21.0-release.0 (********-r-0) ONEPLUS+A3010/9 (OnePlus;ONEPLUS+A3010;OnePlus;OnePlus3;0;;1;2016)',
        'X-Twitter-API-Version': '5',
        'X-Twitter-Client': 'TwitterAndroid',
        'X-Twitter-Client-Version': '10.21.0-release.0',
        'X-Twitter-Active-User': 'yes',
        'Content-Type': 'application/json'
    }
};

// 统计信息
const stats = {
    total: 0,
    success: 0,
    failed: 0,
    startTime: Date.now()
};

// 存储生成的账号
const accounts = [];

// 日志函数
function log(message, level = 'INFO') {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level}] ${message}`;
    console.log(logMessage);
    
    // 写入日志文件
    fs.appendFileSync(config.logFile, logMessage + '\n');
}

// 延迟函数
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// HTTP 请求函数
async function makeRequest(url, options = {}) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), config.timeout);
    
    try {
        const response = await fetch(url, {
            ...options,
            signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        return await response.json();
    } catch (error) {
        clearTimeout(timeoutId);
        throw error;
    }
}

// 获取 Guest Token
async function getGuestToken() {
    const url = `${TWITTER_CONFIG.baseURL}guest/activate.json`;
    
    const response = await makeRequest(url, {
        method: 'POST',
        headers: {
            'Authorization': TWITTER_CONFIG.bearerToken
        }
    });
    
    return response.guest_token;
}

// 获取 Flow Token
async function getFlowToken(guestToken) {
    const url = `${TWITTER_CONFIG.baseURL}onboarding/task.json?flow_name=welcome&api_version=1&known_device_token=&sim_country_code=us`;
    
    const body = {
        flow_token: null,
        input_flow_data: {
            country_code: null,
            flow_context: {
                referrer_context: {
                    referral_details: 'utm_source=google-play&utm_medium=organic',
                    referrer_url: ''
                },
                start_location: {
                    location: 'splash_screen'
                }
            },
            requested_variant: null,
            target_user_id: 0
        },
        subtask_versions: {
            generic_urt: 3,
            standard: 1,
            open_home_timeline: 1,
            app_locale_update: 1,
            enter_date: 1,
            email_verification: 3,
            enter_password: 5,
            enter_text: 5,
            one_tap: 2,
            cta: 7,
            single_sign_on: 1,
            fetch_persisted_data: 1,
            enter_username: 3,
            web_modal: 2,
            fetch_temporary_password: 1,
            menu_dialog: 1,
            sign_up_review: 5,
            interest_picker: 4,
            user_recommendations_urt: 3,
            in_app_notification: 1,
            sign_up: 2,
            typeahead_search: 1,
            user_recommendations_list: 4,
            cta_inline: 1,
            contacts_live_sync_permission_prompt: 3,
            choice_selection: 5,
            js_instrumentation: 1,
            alert_dialog_suppress_client_events: 1,
            privacy_options: 1,
            topics_selector: 1,
            wait_spinner: 3,
            tweet_selection_urt: 1,
            end_flow: 1,
            settings_list: 7,
            open_external_link: 1,
            phone_verification: 5,
            security_key: 3,
            select_banner: 2,
            upload_media: 1,
            web: 2,
            alert_dialog: 1,
            open_account: 2,
            action_list: 2,
            enter_phone: 2,
            open_link: 1,
            show_code: 1,
            update_users: 1,
            check_logged_in_account: 1,
            enter_email: 2,
            select_avatar: 4,
            location_permission_prompt: 2,
            notifications_permission_prompt: 4
        }
    };
    
    const response = await makeRequest(url, {
        method: 'POST',
        headers: {
            ...TWITTER_CONFIG.headers,
            'Authorization': TWITTER_CONFIG.bearerToken,
            'X-Guest-Token': guestToken
        },
        body: JSON.stringify(body)
    });
    
    return response.flow_token;
}

// 创建账号
async function createAccount(guestToken, flowToken) {
    const url = `${TWITTER_CONFIG.baseURL}onboarding/task.json`;
    
    const body = {
        flow_token: flowToken,
        subtask_inputs: [{
            open_link: {
                link: 'next_link'
            },
            subtask_id: 'NextTaskOpenLink'
        }],
        subtask_versions: {
            generic_urt: 3,
            standard: 1,
            open_home_timeline: 1,
            app_locale_update: 1,
            enter_date: 1,
            email_verification: 3,
            enter_password: 5,
            enter_text: 5,
            one_tap: 2,
            cta: 7,
            single_sign_on: 1,
            fetch_persisted_data: 1,
            enter_username: 3,
            web_modal: 2,
            fetch_temporary_password: 1,
            menu_dialog: 1,
            sign_up_review: 5,
            interest_picker: 4,
            user_recommendations_urt: 3,
            in_app_notification: 1,
            sign_up: 2,
            typeahead_search: 1,
            user_recommendations_list: 4,
            cta_inline: 1,
            contacts_live_sync_permission_prompt: 3,
            choice_selection: 5,
            js_instrumentation: 1,
            alert_dialog_suppress_client_events: 1,
            privacy_options: 1,
            topics_selector: 1,
            wait_spinner: 3,
            tweet_selection_urt: 1,
            end_flow: 1,
            settings_list: 7,
            open_external_link: 1,
            phone_verification: 5,
            security_key: 3,
            select_banner: 2,
            upload_media: 1,
            web: 2,
            alert_dialog: 1,
            open_account: 2,
            action_list: 2,
            enter_phone: 2,
            open_link: 1,
            show_code: 1,
            update_users: 1,
            check_logged_in_account: 1,
            enter_email: 2,
            select_avatar: 4,
            location_permission_prompt: 2,
            notifications_permission_prompt: 4
        }
    };
    
    const response = await makeRequest(url, {
        method: 'POST',
        headers: {
            ...TWITTER_CONFIG.headers,
            'Authorization': TWITTER_CONFIG.bearerToken,
            'X-Guest-Token': guestToken
        },
        body: JSON.stringify(body)
    });
    
    // 查找 open_account 任务
    const openAccountTask = response.subtasks?.find(task => task.subtask_id === 'OpenAccount');
    
    if (openAccountTask?.open_account) {
        return openAccountTask.open_account;
    }
    
    return null;
}

// 生成单个账号
async function generateSingleAccount() {
    let retries = 0;

    while (retries < config.maxRetries) {
        try {
            log(`尝试生成账号 (第 ${retries + 1} 次尝试)`);

            // 步骤1: 获取 Guest Token
            const guestToken = await getGuestToken();
            log(`获取到 Guest Token: ${guestToken.substring(0, 20)}...`);

            // 步骤2: 获取 Flow Token
            const flowToken = await getFlowToken(guestToken);
            log(`获取到 Flow Token: ${flowToken.substring(0, 20)}...`);

            // 步骤3: 创建账号
            const account = await createAccount(guestToken, flowToken);

            if (account) {
                const accountInfo = {
                    oauth_token: account.oauth_token,
                    oauth_token_secret: account.oauth_token_secret,
                    user_id: account.user?.id_str,
                    screen_name: account.user?.screen_name,
                    name: account.user?.name,
                    created_at: new Date().toISOString(),
                    guest_token: guestToken
                };

                log(`成功创建账号: ${accountInfo.screen_name} (${accountInfo.user_id})`);
                return accountInfo;
            } else {
                log('账号创建失败: 未返回账号信息', 'WARN');
                retries++;

                if (retries < config.maxRetries) {
                    log(`等待 ${config.requestDelay}ms 后重试...`);
                    await delay(config.requestDelay);
                }
            }

        } catch (error) {
            retries++;
            log(`账号生成失败: ${error.message}`, 'ERROR');

            if (retries < config.maxRetries) {
                log(`等待 ${config.requestDelay * retries}ms 后重试...`);
                await delay(config.requestDelay * retries);
            }
        }
    }

    return null;
}

// 批量生成账号
async function generateAccounts() {
    log(`开始批量生成推特账号，目标数量: ${config.targetAccountCount}`);
    log(`并发数: ${config.concurrency}`);

    const promises = [];
    let completed = 0;

    // 创建并发任务
    for (let i = 0; i < config.concurrency; i++) {
        const promise = (async () => {
            while (accounts.length < config.targetAccountCount) {
                const account = await generateSingleAccount();

                if (account) {
                    accounts.push(account);
                    stats.success++;
                    log(`成功生成账号 ${stats.success}/${config.targetAccountCount}: ${account.screen_name}`);

                    // 保存到文件
                    saveAccounts();
                } else {
                    stats.failed++;
                    log(`账号生成失败，已失败 ${stats.failed} 次`, 'WARN');
                }

                stats.total++;
                completed++;

                // 显示进度
                if (completed % 10 === 0) {
                    showProgress();
                }

                // 请求间隔
                await delay(config.requestDelay);
            }
        })();

        promises.push(promise);
    }

    // 等待所有任务完成
    await Promise.all(promises);

    log(`批量生成完成！成功: ${stats.success}, 失败: ${stats.failed}`);
    showFinalStats();
}

// 显示进度
function showProgress() {
    const elapsed = Date.now() - stats.startTime;
    const rate = stats.total / (elapsed / 1000);
    const eta = (config.targetAccountCount - accounts.length) / rate;

    log(`进度: ${accounts.length}/${config.targetAccountCount} (${(accounts.length/config.targetAccountCount*100).toFixed(1)}%)`);
    log(`速率: ${rate.toFixed(2)} 账号/秒, 预计剩余时间: ${Math.round(eta)}秒`);
}

// 显示最终统计
function showFinalStats() {
    const elapsed = Date.now() - stats.startTime;
    const avgRate = stats.total / (elapsed / 1000);

    log('='.repeat(50));
    log('最终统计:');
    log(`总耗时: ${Math.round(elapsed / 1000)}秒`);
    log(`成功账号: ${stats.success}`);
    log(`失败次数: ${stats.failed}`);
    log(`成功率: ${(stats.success / stats.total * 100).toFixed(1)}%`);
    log(`平均速率: ${avgRate.toFixed(2)} 账号/秒`);
    log(`账号保存位置: ${config.outputFile}`);
    log('='.repeat(50));
}

// 保存账号到文件
function saveAccounts() {
    try {
        const data = {
            generated_at: new Date().toISOString(),
            total_count: accounts.length,
            accounts: accounts
        };

        fs.writeFileSync(config.outputFile, JSON.stringify(data, null, 2));

        // 同时保存为简单格式 (兼容其他脚本)
        const simpleFormat = {
            oauth_tokens: accounts.map(acc => acc.oauth_token),
            oauth_token_secrets: accounts.map(acc => acc.oauth_token_secret),
            screen_names: accounts.map(acc => acc.screen_name)
        };

        const simpleFile = config.outputFile.replace('.json', '_simple.txt');
        const simpleContent = [
            `TWITTER_OAUTH_TOKEN=${simpleFormat.oauth_tokens.join(',')}`,
            `TWITTER_OAUTH_TOKEN_SECRET=${simpleFormat.oauth_token_secrets.join(',')}`,
            `TWITTER_SCREEN_NAMES=${simpleFormat.screen_names.join(',')}`
        ].join('\n');

        fs.writeFileSync(simpleFile, simpleContent);

    } catch (error) {
        log(`保存账号失败: ${error.message}`, 'ERROR');
    }
}

// 加载已有账号
function loadExistingAccounts() {
    try {
        if (fs.existsSync(config.outputFile)) {
            const data = JSON.parse(fs.readFileSync(config.outputFile, 'utf8'));
            if (data.accounts && Array.isArray(data.accounts)) {
                accounts.push(...data.accounts);
                log(`加载了 ${data.accounts.length} 个已有账号`);
            }
        }
    } catch (error) {
        log(`加载已有账号失败: ${error.message}`, 'WARN');
    }
}

// 验证账号有效性
async function validateAccount(account) {
    try {
        // 这里可以添加账号验证逻辑
        // 例如尝试获取用户信息来验证账号是否有效
        return true;
    } catch (error) {
        log(`账号验证失败: ${account.screen_name} - ${error.message}`, 'WARN');
        return false;
    }
}

// 主函数
async function main() {
    try {
        log('推特批量注册脚本启动');
        log(`配置: 目标数量=${config.targetAccountCount}, 并发数=${config.concurrency}`);

        // 创建输出目录
        const outputDir = path.dirname(config.outputFile);
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        // 加载已有账号
        loadExistingAccounts();

        if (accounts.length >= config.targetAccountCount) {
            log(`已有 ${accounts.length} 个账号，达到目标数量`);
            return;
        }

        log(`需要生成 ${config.targetAccountCount - accounts.length} 个新账号`);

        // 开始生成账号
        await generateAccounts();

        log('脚本执行完成');

    } catch (error) {
        log(`脚本执行失败: ${error.message}`, 'ERROR');
        process.exit(1);
    }
}

// 处理程序退出
process.on('SIGINT', () => {
    log('收到中断信号，正在保存已生成的账号...');
    saveAccounts();
    showFinalStats();
    process.exit(0);
});

process.on('uncaughtException', (error) => {
    log(`未捕获的异常: ${error.message}`, 'ERROR');
    saveAccounts();
    process.exit(1);
});

// 启动脚本
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}
