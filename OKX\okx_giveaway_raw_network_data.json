{
  "success": true,
  "message": "Capture for tab 159821864 (https://web3.okx.com/zh-hans/giveaway/jaspervault) stopped. 23 requests captured.",
  "tabId": 159821864,
  "tabUrl": "https://web3.okx.com/zh-hans/giveaway/jaspervault",
  "tabTitle": "Jasper Vault 价值 50K USDT 代币奖励活动 | 赢取 OKX Wallet x Jasper Vault Giveaway 空投 | OKX Wallet",
  "requestCount": 23,
  "commonRequestHeaders": {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36",
    "sec-ch-ua": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\""
  },
  "commonResponseHeaders": {
    "cf-cache-status": "DYNAMIC",
    "content-security-policy": "frame-ancestors 'self'",
    "server": "cloudflare",
    "x-brokerid": "0"
  },
  "captureStartTime": 1754519347896,
  "captureEndTime": 1754519402841,
  "totalDurationMs": 54945,
  "settingsUsed": {},
  "remainingCaptures": [159821943, 159821944, 159821945, 159821946],
  "totalRequestsReceived": 23,
  "requestLimitReached": false,
  "keyRequests": [
    {
      "type": "clickTask",
      "url": "https://web3.okx.com/priapi/v1/dapp/giveaway/clickTask?t=1754519361856",
      "method": "POST",
      "requestBody": "{\"giveawayId\":389,\"taskId\":618,\"walletAddress\":\"******************************************\"}",
      "responseBody": "{\"code\":0,\"data\":{},\"detailMsg\":\"\",\"error_code\":\"0\",\"error_message\":\"\",\"msg\":\"\"}",
      "statusCode": 200,
      "description": "点击第一个任务 - 关注 @Jaspervault 的 X"
    },
    {
      "type": "clickTask", 
      "url": "https://web3.okx.com/priapi/v1/dapp/giveaway/clickTask?t=1754519365777",
      "method": "POST",
      "requestBody": "{\"giveawayId\":389,\"taskId\":619,\"walletAddress\":\"******************************************\"}",
      "responseBody": "{\"code\":0,\"data\":{},\"detailMsg\":\"\",\"error_code\":\"0\",\"error_message\":\"\",\"msg\":\"\"}",
      "statusCode": 200,
      "description": "点击第二个任务 - 加入 Jasper Vault 官方 Discord 社区"
    },
    {
      "type": "taskCheck",
      "url": "https://web3.okx.com/priapi/v1/dapp/giveaway/task/check?t=*************",
      "method": "POST", 
      "requestBody": "{\"taskId\":619,\"giveawayId\":389,\"walletAddress\":\"******************************************\",\"walletAccountId\":\"BB9AB302-2EA1-4625-85E2-89F267E3D624\",\"userUniqueId\":\"\"}",
      "responseBody": "{\"code\":0,\"data\":{\"committedAddress\":\"\",\"email\":\"\",\"status\":1},\"detailMsg\":\"\",\"error_code\":\"0\",\"error_message\":\"\",\"msg\":\"\"}",
      "statusCode": 200,
      "description": "检查第二个任务状态"
    },
    {
      "type": "clickTask",
      "url": "https://web3.okx.com/priapi/v1/dapp/giveaway/clickTask?t=*************", 
      "method": "POST",
      "requestBody": "{\"giveawayId\":389,\"taskId\":620,\"walletAddress\":\"******************************************\"}",
      "responseBody": "{\"code\":0,\"data\":{},\"detailMsg\":\"\",\"error_code\":\"0\",\"error_message\":\"\",\"msg\":\"\"}",
      "statusCode": 200,
      "description": "点击第三个任务 - 连接 OKX Wallet 并参与活动"
    },
    {
      "type": "verify",
      "url": "https://web3.okx.com/priapi/v1/dapp/giveaway/verify?t=*************",
      "method": "POST",
      "requestBody": "{\"giveawayId\":389,\"walletAddress\":\"******************************************\",\"walletAccountId\":\"BB9AB302-2EA1-4625-85E2-89F267E3D624\",\"userUniqueId\":\"\"}",
      "responseBody": "{\"code\":0,\"data\":{\"verifyFailCause\":0,\"verifySucceed\":true},\"detailMsg\":\"\",\"error_code\":\"0\",\"error_message\":\"\",\"msg\":\"\"}",
      "statusCode": 200,
      "description": "验证所有任务完成 - 成功"
    },
    {
      "type": "getDetailV2",
      "url": "https://web3.okx.com/priapi/v1/dapp/giveaway/getDetailV2?t=*************",
      "method": "POST",
      "requestBody": "{\"navName\":\"jaspervault\",\"walletAddress\":{\"8453\":\"******************************************\"}}",
      "responseBody": "{\"code\":0,\"data\":{\"appJoinLink\":\"\",\"chainId\":8453,\"dappName\":\"Jasper Vault\",\"drawType\":0,\"endTime\":*************,\"giveawayStatus\":0,\"id\":389,\"info\":\"我们与 Jasper Vault 合作推出 50K USDT 等值 Jasper Vault 代币奖励活动，给予 5,000 名参加者。每位获奖者将在 TGE 后获得价值 $10 的代币。奖励将由 Jasper Vault 团队独立发放。\",\"localTime\":1754519388366,\"logo\":\"https://web3.okx.com/cdn/nft/194fc597-2e92-4a4c-9692-83e3dfbb6e40.jpg?x-oss-process=image/format,webp/resize,w_300/ignore-error,1\",\"luckNum\":\"\",\"minimumSupportAppVersion\":\"\",\"name\":\"Jasper Vault 价值 50K USDT 代币奖励活动\",\"navName\":\"JasperVault\",\"newFlag\":false,\"pcJoinLink\":\"\",\"playMode\":1,\"preList\":[],\"reward\":\"5 万美元等值 Jasper Vault 代币\",\"startTime\":1753347600000,\"tag\":1,\"taskList\":[{\"appJumpUrl\":\"https://x.com/jaspervault\",\"bindStatus\":true,\"bindUserName\":\"Ja***04\",\"committedChainId\":0,\"committedChainName\":\"\",\"id\":618,\"info\":\"点击"连接账号"连接 X 账号后，你需要再次点击跳转去关注项目方官方 X 账号 https://x.com/jaspervault\",\"name\":\"关注 @Jaspervault 的 X\",\"pcJumpUrl\":\"https://x.com/jaspervault\",\"playType\":1,\"taskType\":1,\"taskType2\":0},{\"appJumpUrl\":\"https://discord.gg/jaspervault\",\"bindStatus\":true,\"bindUserName\":\"Ja***04\",\"committedChainId\":0,\"committedChainName\":\"\",\"id\":619,\"info\":\"加入 Jasper Vault 官方 Discord 社区 https://discord.gg/jaspervault\",\"name\":\"加入 Jasper Vault 官方 Discord 社区\",\"pcJumpUrl\":\"https://discord.gg/jaspervault\",\"playType\":1,\"taskType\":4,\"taskType2\":0},{\"appJumpUrl\":\"https://app.jaspervault.io/pro/#/activityCenter\",\"bindStatus\":true,\"bindUserName\":\"Ja***04\",\"committedChainId\":0,\"committedChainName\":\"\",\"id\":620,\"info\":\"在 https://app.jaspervault.io/pro/#/activityCenter 连接 OKX Wallet，点击参与活动即可完成任务。\",\"name\":\"连接 OKX Wallet 并参与活动\",\"pcJumpUrl\":\"https://app.jaspervault.io/pro/#/activityCenter\",\"playType\":1,\"taskType\":4,\"taskType2\":0},{\"appJumpUrl\":\"https://x.com/wallet\",\"bindStatus\":true,\"bindUserName\":\"Ja***04\",\"committedChainId\":0,\"committedChainName\":\"\",\"id\":621,\"info\":\"点击"连接账号"连接 X 账号后，你需要再次点击跳转去关注项目方官方 X 账号 https://x.com/wallet\",\"name\":\"关注 @wallet 的 X\",\"pcJumpUrl\":\"https://x.com/wallet\",\"playType\":1,\"taskType\":1,\"taskType2\":0},{\"appJumpUrl\":\"\",\"bindStatus\":true,\"bindUserName\":\"Ja***04\",\"committedChainId\":0,\"committedChainName\":\"\",\"id\":622,\"info\":\"OKX Wallet 持有至少 10 USDT 等值代币资产达 24 小时\",\"name\":\"OKX Wallet 持有至少 10 USDT 等值代币\",\"pcJumpUrl\":\"\",\"playType\":0,\"taskType\":2,\"taskType2\":0}],\"tutorial\":\"\",\"twitter\":\"https://x.com/jaspervault\",\"userCount\":653686,\"userNum\":\"\",\"verified\":true,\"winStatus\":0,\"winnerCount\":5000,\"winnerJumpUrl\":\"\"},\"detailMsg\":\"\",\"error_code\":\"0\",\"error_message\":\"\",\"msg\":\"\"}",
      "statusCode": 200,
      "description": "获取Giveaway详情和任务列表"
    },
    {
      "type": "checkAll",
      "url": "https://web3.okx.com/priapi/v1/dapp/giveaway/task/checkAll?t=*************",
      "method": "POST",
      "requestBody": "{\"giveawayId\":389,\"walletAddress\":{\"8453\":\"******************************************\"},\"walletAccountId\":\"BB9AB302-2EA1-4625-85E2-89F267E3D624\",\"userUniqueId\":\"\"}",
      "responseBody": "{\"code\":0,\"data\":{\"620\":{\"committedAddress\":\"\",\"email\":\"\",\"status\":1},\"621\":{\"committedAddress\":\"\",\"email\":\"\",\"status\":1},\"622\":{\"committedAddress\":\"\",\"email\":\"\",\"status\":1},\"618\":{\"committedAddress\":\"\",\"email\":\"\",\"status\":1},\"619\":{\"committedAddress\":\"\",\"email\":\"\",\"status\":1}},\"detailMsg\":\"\",\"error_code\":\"0\",\"error_message\":\"\",\"msg\":\"\"}",
      "statusCode": 200,
      "description": "检查所有任务状态 - 全部完成"
    }
  ]
}
