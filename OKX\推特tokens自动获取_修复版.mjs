// 推特 Tokens 自动获取脚本 - 修复版
// 解决可疑登录和错误处理问题

import fs from 'fs';

// 配置区域 - 需要填入真实的推特账号信息
const accounts = [
    {
        account: 'bdumdums',           // 邮箱或用户名或手机号
        screen_name: 'bdumdums',      // 用户名或手机号
        password: 'NguuVQy2Ed4sYt3',  // 密码
        _2fa: '',                     // TOTP 二次验证码（如果有）
        acid: ''                      // 邮箱验证码（如果需要）
    }
];

// Twitter API 配置
const bearer_token = 'Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA';

// 延迟函数
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// 发送登录请求
const sendLoginRequest = async (bearer_token, guest_token, cookie = {}, headers = {}, query = new URLSearchParams({}), body = {}) => {
    try {
        const response = await fetch(`https://api.twitter.com/1.1/onboarding/task.json${query.size > 0 ? `?${query.toString()}` : ''}`, {
            method: 'POST',
            headers: {
                'content-type': 'application/json',
                authorization: bearer_token,
                'x-guest-token': guest_token,
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                cookie: Object.entries(cookie)
                    .map(([key, value]) => `${key}=${value}`)
                    .join('; '),
                ...headers
            },
            body: JSON.stringify(body)
        });

        const cookies = Object.fromEntries(
            [...response.headers.entries()]
                .filter((header) => header[0] === 'set-cookie')
                .map((header) => {
                    const tmpCookie = header[1].split(';')[0];
                    const firstEqual = tmpCookie.indexOf('=');
                    return [tmpCookie.slice(0, firstEqual), tmpCookie.slice(firstEqual + 1)];
                })
        );

        const content = await response.json();
        
        return {
            success: response.ok,
            status: response.status,
            cookies,
            content,
            headers: response.headers
        };
    } catch (error) {
        console.error('❌ 请求失败:', error.message);
        return {
            success: false,
            error: error.message,
            cookies: {},
            content: {},
            headers: new Map()
        };
    }
};

// 检查是否被阻止登录
function checkForLoginBlock(response) {
    if (response.content?.subtasks) {
        const denyTask = response.content.subtasks.find(task => task.subtask_id === 'DenyLoginSubtask');
        if (denyTask) {
            return {
                blocked: true,
                reason: denyTask.cta?.secondary_text?.text || '登录被阻止'
            };
        }
    }
    return { blocked: false };
}

// 检查是否有错误
function checkForErrors(response) {
    if (response.content?.errors && response.content.errors.length > 0) {
        return {
            hasError: true,
            errors: response.content.errors
        };
    }
    return { hasError: false };
}

// 单个账号登录并获取 tokens
async function loginAndGetTokens(accountConfig) {
    console.log(`\n🔐 开始登录账号: ${accountConfig.account}`);
    
    try {
        // 获取 guest token
        console.log('📡 获取 guest token...');
        const guestResponse = await fetch('https://api.twitter.com/1.1/guest/activate.json', {
            method: 'POST',
            headers: {
                authorization: bearer_token,
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
        });

        if (!guestResponse.ok) {
            throw new Error(`获取 guest token 失败: ${guestResponse.status}`);
        }

        const guestData = await guestResponse.json();
        const guest_token = guestData.guest_token;

        console.log(`✅ Guest token: ${guest_token.substring(0, 20)}...`);

        let cookie = {};
        let headers = {};

        // 添加延迟避免被检测
        await delay(2000);

        // 开始登录流程
        console.log('🚀 开始登录流程...');
        const login = await sendLoginRequest(
            bearer_token,
            guest_token,
            cookie,
            headers,
            new URLSearchParams({
                flow_name: 'login'
            }),
            {
                input_flow_data: { 
                    flow_context: { 
                        debug_overrides: {}, 
                        start_location: { location: 'manual_link' } 
                    } 
                },
                subtask_versions: {
                    action_list: 2,
                    alert_dialog: 1,
                    app_download_cta: 1,
                    check_logged_in_account: 1,
                    choice_selection: 3,
                    contacts_live_sync_permission_prompt: 0,
                    cta: 7,
                    email_verification: 2,
                    end_flow: 1,
                    enter_date: 1,
                    enter_email: 2,
                    enter_password: 5,
                    enter_phone: 2,
                    enter_recaptcha: 1,
                    enter_text: 5,
                    enter_username: 2,
                    generic_urt: 3,
                    in_app_notification: 1,
                    interest_picker: 3,
                    js_instrumentation: 1,
                    menu_dialog: 1,
                    notifications_permission_prompt: 2,
                    open_account: 2,
                    open_home_timeline: 1,
                    open_link: 1,
                    phone_verification: 4,
                    privacy_options: 1,
                    security_key: 3,
                    select_avatar: 4,
                    select_banner: 2,
                    settings_list: 7,
                    show_code: 1,
                    sign_up: 2,
                    sign_up_review: 4,
                    tweet_selection_urt: 1,
                    update_users: 1,
                    upload_media: 1,
                    user_recommendations_list: 4,
                    user_recommendations_urt: 1,
                    wait_spinner: 3,
                    web_modal: 1
                }
            }
        );

        if (!login.success) {
            throw new Error(`登录请求失败: ${login.error || login.status}`);
        }

        // 检查是否被阻止
        const blockCheck = checkForLoginBlock(login);
        if (blockCheck.blocked) {
            throw new Error(`登录被阻止: ${blockCheck.reason}`);
        }

        // 检查错误
        const errorCheck = checkForErrors(login);
        if (errorCheck.hasError) {
            const errorMsg = errorCheck.errors.map(e => `${e.code}: ${e.message}`).join(', ');
            throw new Error(`API 错误: ${errorMsg}`);
        }

        cookie = { ...cookie, ...login.cookies };
        let response = login.content;

        console.log(`📡 登录流程状态: ${response.status || '未知'}`);

        // 检查是否有有效的 flow_token
        if (!response.flow_token) {
            throw new Error('未获取到有效的 flow_token');
        }

        await delay(1000);

        // JS Instrumentation (网页版需要)
        console.log('🔧 执行 JS Instrumentation...');
        const jsInstrumentation = await sendLoginRequest(bearer_token, guest_token, cookie, headers, new URLSearchParams({}), {
            flow_token: response.flow_token,
            subtask_inputs: [
                {
                    js_instrumentation: {
                        link: 'next_link',
                        response: '{}'
                    },
                    subtask_id: 'LoginJsInstrumentationSubtask'
                }
            ]
        });

        if (jsInstrumentation.success) {
            cookie = { ...cookie, ...jsInstrumentation.cookies };
            response = jsInstrumentation.content;
        }

        await delay(1000);

        // 输入用户标识符
        console.log('👤 输入用户标识符...');
        const userIdentifier = await sendLoginRequest(bearer_token, guest_token, cookie, headers, new URLSearchParams({}), {
            flow_token: response.flow_token,
            subtask_inputs: [
                {
                    settings_list: {
                        link: 'next_link',
                        setting_responses: [
                            {
                                key: 'user_identifier',
                                response_data: {
                                    text_data: {
                                        result: accountConfig.account
                                    }
                                }
                            }
                        ]
                    },
                    subtask_id: 'LoginEnterUserIdentifierSSO'
                }
            ]
        });

        if (!userIdentifier.success) {
            throw new Error('用户标识符提交失败');
        }

        // 检查是否被阻止
        const userBlockCheck = checkForLoginBlock(userIdentifier);
        if (userBlockCheck.blocked) {
            throw new Error(`用户验证被阻止: ${userBlockCheck.reason}`);
        }

        cookie = { ...cookie, ...userIdentifier.cookies };
        response = userIdentifier.content;

        // 安全检查：确保 subtasks 存在
        if (!response.subtasks || !Array.isArray(response.subtasks)) {
            throw new Error('响应格式异常：缺少 subtasks');
        }

        await delay(1000);

        // 处理备用标识符（如果需要）
        const alternateTask = response.subtasks.find(task => task.subtask_id === 'LoginEnterAlternateIdentifierSubtask');
        if (alternateTask) {
            console.log('🔄 输入备用标识符...');
            const alternateIdentifier = await sendLoginRequest(bearer_token, guest_token, cookie, headers, new URLSearchParams({}), {
                flow_token: response.flow_token,
                subtask_inputs: [
                    {
                        enter_text: {
                            link: 'next_link',
                            text: accountConfig.screen_name
                        },
                        subtask_id: 'LoginEnterAlternateIdentifierSubtask'
                    }
                ]
            });

            if (alternateIdentifier.success) {
                cookie = { ...cookie, ...alternateIdentifier.cookies };
                response = alternateIdentifier.content;
            }
            await delay(1000);
        }

        // 输入密码
        console.log('🔑 输入密码...');
        const passwordTask = response.subtasks.find(task => task.subtask_id === 'LoginEnterPassword');
        if (!passwordTask) {
            throw new Error('未找到密码输入任务');
        }

        const passwordResponse = await sendLoginRequest(bearer_token, guest_token, cookie, headers, new URLSearchParams({}), {
            flow_token: response.flow_token,
            subtask_inputs: [
                {
                    enter_password: {
                        link: 'next_link',
                        password: accountConfig.password
                    },
                    subtask_id: 'LoginEnterPassword'
                }
            ]
        });

        if (!passwordResponse.success) {
            throw new Error('密码验证失败');
        }

        cookie = { ...cookie, ...passwordResponse.cookies };
        response = passwordResponse.content;

        console.log('✅ 密码验证成功');

        // 提取关键的认证信息
        const tokens = {
            timestamp: new Date().toISOString(),
            account: accountConfig.account,
            screen_name: accountConfig.screen_name,
            tokens: {
                cookies: {
                    auth_token: cookie.auth_token,
                    ct0: cookie.ct0,
                    twid: cookie.twid,
                    sess: cookie.sess,
                    kdt: cookie.kdt,
                    remember_checked_on: cookie.remember_checked_on,
                    guest_id: cookie.guest_id,
                    guest_id_marketing: cookie.guest_id_marketing,
                    guest_id_ads: cookie.guest_id_ads,
                    personalization_id: cookie.personalization_id
                },
                localStorage: {}
            }
        };

        console.log(`✅ 成功获取 ${accountConfig.account} 的 tokens`);
        console.log(`🍪 获取到 ${Object.keys(tokens.tokens.cookies).filter(k => tokens.tokens.cookies[k]).length} 个有效 cookies`);
        
        return tokens;

    } catch (error) {
        console.error(`❌ 登录失败 ${accountConfig.account}:`, error.message);
        return null;
    }
}

// 保存 tokens 到文件
function saveTokensToFile(tokens, filename = 'twitter_tokens.json') {
    try {
        if (tokens.length === 1) {
            // 单个账号，保存为单个 JSON 对象
            fs.writeFileSync(filename, JSON.stringify(tokens[0], null, 2));
        } else {
            // 多个账号，保存为 JSON 数组
            fs.writeFileSync(filename, JSON.stringify(tokens, null, 2));
        }

        console.log(`💾 Tokens 已保存到: ${filename}`);
        return true;
    } catch (error) {
        console.error(`❌ 保存文件失败: ${error.message}`);
        return false;
    }
}

// 验证配置
function validateConfig() {
    if (accounts.length === 0) {
        console.error('❌ 未配置任何账号信息');
        console.log('📝 请在脚本顶部的 accounts 数组中添加账号信息');
        return false;
    }

    for (let i = 0; i < accounts.length; i++) {
        const account = accounts[i];
        if (!account.account || !account.password) {
            console.error(`❌ 账号 ${i + 1} 缺少必要信息 (account 或 password)`);
            return false;
        }
    }

    return true;
}

// 主函数
async function main() {
    console.log('🎯 Twitter Tokens 自动获取脚本 - 修复版');
    console.log('=' .repeat(50));

    // 验证配置
    if (!validateConfig()) {
        process.exit(1);
    }

    console.log(`📊 总共 ${accounts.length} 个账号需要处理\n`);

    const allTokens = [];
    const results = {
        success: 0,
        failed: 0,
        errors: []
    };

    for (let i = 0; i < accounts.length; i++) {
        const account = accounts[i];
        console.log(`\n📋 进度: ${i + 1}/${accounts.length}`);
        console.log(`🔐 处理账号: ${account.account}`);

        try {
            const tokens = await loginAndGetTokens(account);

            if (tokens) {
                allTokens.push(tokens);
                results.success++;
                console.log(`✅ 账号 ${i + 1} 处理成功`);
            } else {
                results.failed++;
                results.errors.push(`账号 ${i + 1} (${account.account}) 登录失败`);
                console.log(`❌ 账号 ${i + 1} 处理失败`);
            }

        } catch (error) {
            results.failed++;
            results.errors.push(`账号 ${i + 1} (${account.account}) 异常: ${error.message}`);
            console.error(`❌ 账号 ${i + 1} 处理异常:`, error.message);
        }

        // 避免请求过于频繁
        if (i < accounts.length - 1) {
            console.log('⏸️ 等待 10 秒后处理下一个账号...');
            await delay(10000);
        }
    }

    // 显示结果摘要
    console.log('\n📊 执行结果摘要:');
    console.log('=' .repeat(50));
    console.log(`✅ 成功: ${results.success} 个账号`);
    console.log(`❌ 失败: ${results.failed} 个账号`);
    console.log(`📈 成功率: ${(results.success / accounts.length * 100).toFixed(1)}%`);

    if (results.errors.length > 0) {
        console.log('\n❌ 错误详情:');
        results.errors.forEach(error => console.log(`   - ${error}`));
    }

    // 保存成功的 tokens
    if (allTokens.length > 0) {
        console.log(`\n💾 保存 ${allTokens.length} 个有效 tokens...`);

        // 保存主文件
        saveTokensToFile(allTokens, 'twitter_tokens.json');

        // 同时保存备份文件
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        saveTokensToFile(allTokens, `twitter_tokens_backup_${timestamp}.json`);

        console.log('\n🎉 Twitter Tokens 获取完成！');
        console.log('📁 文件已生成:');
        console.log('   - twitter_tokens.json (主文件)');
        console.log(`   - twitter_tokens_backup_${timestamp}.json (备份文件)`);

        // 显示获取到的 cookies 信息
        console.log('\n🍪 获取到的 Cookies 信息:');
        allTokens.forEach((token, index) => {
            const cookieCount = Object.keys(token.tokens.cookies).filter(k => token.tokens.cookies[k]).length;
            console.log(`   账号 ${index + 1} (@${token.screen_name}): ${cookieCount} 个有效 cookies`);
        });

    } else {
        console.log('\n❌ 未获取到任何有效的 tokens');
        console.log('\n💡 可能的解决方案:');
        console.log('   1. 检查账号密码是否正确');
        console.log('   2. 确认账号未被锁定或限制');
        console.log('   3. 尝试使用不同的网络环境');
        console.log('   4. 等待一段时间后重试');
        console.log('   5. 考虑使用代理服务');
    }
}

// 运行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(error => {
        console.error('❌ 脚本执行失败:', error.message);
        process.exit(1);
    });
}
