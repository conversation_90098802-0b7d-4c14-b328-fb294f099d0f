/**
 * Twitter AuthToken 提取器
 * 用于从浏览器中提取Twitter的认证令牌
 * 使用方法：在Twitter页面的控制台中运行此脚本
 */

class TwitterAuthExtractor {
    constructor() {
        this.tokens = {
            cookies: {},
            localStorage: {},
            sessionStorage: {},
            networkHeaders: {},
            pageVariables: {},
            apiTokens: {}
        };
        this.setupNetworkInterceptor();
    }

    /**
     * 主要提取方法
     */
    async extractAllTokens() {
        console.log('🔍 开始提取Twitter AuthToken...');
        
        // 1. 提取Cookies
        this.extractCookies();
        
        // 2. 提取LocalStorage
        this.extractLocalStorage();
        
        // 3. 提取SessionStorage
        this.extractSessionStorage();
        
        // 4. 提取页面变量
        this.extractPageVariables();
        
        // 5. 尝试获取用户信息
        await this.extractUserInfo();
        
        // 6. 分析和格式化结果
        const result = this.analyzeTokens();
        
        console.log('✅ 提取完成！');
        console.log('📊 提取结果:', result);
        
        return result;
    }

    /**
     * 提取Cookies中的认证信息
     */
    extractCookies() {
        console.log('🍪 提取Cookies...');
        
        const authCookieNames = [
            'auth_token',           // 主要认证令牌
            'ct0',                  // CSRF令牌
            'twid',                 // Twitter ID
            'kdt',                  // 密钥派生令牌
            'remember_checked_on',  // 记住登录状态
            'personalization_id',   // 个性化ID
            'guest_id',            // 访客ID
            'att'                  // 认证跟踪令牌
        ];

        const cookies = document.cookie.split(';');
        
        cookies.forEach(cookie => {
            const [name, value] = cookie.trim().split('=');
            if (authCookieNames.some(authName => name.includes(authName))) {
                this.tokens.cookies[name] = value;
                console.log(`  ✓ 找到Cookie: ${name} = ${value?.substring(0, 20)}...`);
            }
        });
    }

    /**
     * 提取LocalStorage中的认证信息
     */
    extractLocalStorage() {
        console.log('💾 提取LocalStorage...');
        
        const authKeys = [
            'twitter_auth',
            'user_token',
            'access_token',
            'oauth_token',
            'bearer_token',
            'auth_data',
            'user_data'
        ];

        Object.keys(localStorage).forEach(key => {
            const lowerKey = key.toLowerCase();
            if (authKeys.some(authKey => lowerKey.includes(authKey)) || 
                lowerKey.includes('auth') || 
                lowerKey.includes('token')) {
                
                try {
                    const value = localStorage.getItem(key);
                    this.tokens.localStorage[key] = value;
                    console.log(`  ✓ 找到LocalStorage: ${key} = ${value?.substring(0, 30)}...`);
                } catch (e) {
                    console.log(`  ⚠️ 无法读取LocalStorage: ${key}`);
                }
            }
        });
    }

    /**
     * 提取SessionStorage中的认证信息
     */
    extractSessionStorage() {
        console.log('🗂️ 提取SessionStorage...');
        
        Object.keys(sessionStorage).forEach(key => {
            const lowerKey = key.toLowerCase();
            if (lowerKey.includes('auth') || 
                lowerKey.includes('token') || 
                lowerKey.includes('twitter')) {
                
                try {
                    const value = sessionStorage.getItem(key);
                    this.tokens.sessionStorage[key] = value;
                    console.log(`  ✓ 找到SessionStorage: ${key} = ${value?.substring(0, 30)}...`);
                } catch (e) {
                    console.log(`  ⚠️ 无法读取SessionStorage: ${key}`);
                }
            }
        });
    }

    /**
     * 提取页面变量中的认证信息
     */
    extractPageVariables() {
        console.log('🌐 提取页面变量...');
        
        // 检查常见的全局变量
        const globalVars = [
            'window.__INITIAL_STATE__',
            'window.twttr',
            'window._twitter_sess',
            'window.TD',
            'window.tweetdeck'
        ];

        globalVars.forEach(varPath => {
            try {
                const value = eval(varPath);
                if (value) {
                    this.tokens.pageVariables[varPath] = this.extractAuthFromObject(value);
                    console.log(`  ✓ 找到页面变量: ${varPath}`);
                }
            } catch (e) {
                // 变量不存在，忽略
            }
        });

        // 检查页面中的script标签
        this.extractFromScriptTags();
    }

    /**
     * 从script标签中提取认证信息
     */
    extractFromScriptTags() {
        const scripts = document.querySelectorAll('script');
        
        scripts.forEach((script, index) => {
            if (script.textContent) {
                const content = script.textContent;
                
                // 查找可能的token模式
                const tokenPatterns = [
                    /auth_token["\s]*[:=]["\s]*([a-zA-Z0-9_-]+)/gi,
                    /bearer["\s]*[:=]["\s]*([a-zA-Z0-9_.-]+)/gi,
                    /access_token["\s]*[:=]["\s]*([a-zA-Z0-9_.-]+)/gi,
                    /"token"["\s]*:["\s]*"([a-zA-Z0-9_.-]+)"/gi
                ];

                tokenPatterns.forEach(pattern => {
                    const matches = content.match(pattern);
                    if (matches) {
                        matches.forEach(match => {
                            console.log(`  ✓ 在Script标签中找到: ${match.substring(0, 50)}...`);
                            this.tokens.pageVariables[`script_${index}`] = match;
                        });
                    }
                });
            }
        });
    }

    /**
     * 设置网络请求拦截器
     */
    setupNetworkInterceptor() {
        console.log('🌐 设置网络拦截器...');
        
        // 拦截fetch请求
        const originalFetch = window.fetch;
        window.fetch = (...args) => {
            const [url, options] = args;
            
            if (this.isTwitterAPI(url)) {
                this.extractFromNetworkRequest(url, options);
            }
            
            return originalFetch.apply(window, args);
        };

        // 拦截XMLHttpRequest
        const originalXHROpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function(method, url, ...rest) {
            if (this.isTwitterAPI && this.isTwitterAPI(url)) {
                this.addEventListener('readystatechange', () => {
                    if (this.readyState === 4) {
                        this.extractFromXHRResponse && this.extractFromXHRResponse(this);
                    }
                });
            }
            return originalXHROpen.apply(this, [method, url, ...rest]);
        };
    }

    /**
     * 判断是否为Twitter API请求
     */
    isTwitterAPI(url) {
        return url.includes('twitter.com') || 
               url.includes('x.com') || 
               url.includes('api.twitter.com') ||
               url.includes('api.x.com');
    }

    /**
     * 从网络请求中提取认证信息
     */
    extractFromNetworkRequest(url, options) {
        if (options && options.headers) {
            Object.keys(options.headers).forEach(header => {
                if (header.toLowerCase().includes('authorization') || 
                    header.toLowerCase().includes('auth')) {
                    
                    this.tokens.networkHeaders[header] = options.headers[header];
                    console.log(`  ✓ 网络请求头: ${header} = ${options.headers[header]?.substring(0, 30)}...`);
                }
            });
        }
    }

    /**
     * 尝试获取用户信息
     */
    async extractUserInfo() {
        console.log('👤 尝试获取用户信息...');
        
        try {
            // 尝试从页面DOM中获取用户信息
            const userInfo = this.extractUserFromDOM();
            if (userInfo) {
                this.tokens.userInfo = userInfo;
                console.log('  ✓ 从DOM获取用户信息:', userInfo);
            }
        } catch (e) {
            console.log('  ⚠️ 无法从DOM获取用户信息');
        }
    }

    /**
     * 从DOM中提取用户信息
     */
    extractUserFromDOM() {
        // 尝试多种方法获取用户信息
        const methods = [
            () => document.querySelector('[data-testid="UserName"]')?.textContent,
            () => document.querySelector('[data-testid="UserScreenName"]')?.textContent,
            () => document.querySelector('h1[role="heading"]')?.textContent,
            () => document.querySelector('[aria-label*="@"]')?.getAttribute('aria-label')
        ];

        const userInfo = {};
        
        methods.forEach((method, index) => {
            try {
                const result = method();
                if (result) {
                    userInfo[`method_${index}`] = result;
                }
            } catch (e) {
                // 忽略错误
            }
        });

        return Object.keys(userInfo).length > 0 ? userInfo : null;
    }

    /**
     * 从对象中递归提取认证相关信息
     */
    extractAuthFromObject(obj, depth = 0) {
        if (depth > 3 || !obj || typeof obj !== 'object') return null;
        
        const authData = {};
        
        Object.keys(obj).forEach(key => {
            const lowerKey = key.toLowerCase();
            if (lowerKey.includes('auth') || 
                lowerKey.includes('token') || 
                lowerKey.includes('user') ||
                lowerKey.includes('session')) {
                
                authData[key] = obj[key];
            } else if (typeof obj[key] === 'object') {
                const nested = this.extractAuthFromObject(obj[key], depth + 1);
                if (nested && Object.keys(nested).length > 0) {
                    authData[key] = nested;
                }
            }
        });

        return Object.keys(authData).length > 0 ? authData : null;
    }

    /**
     * 分析和格式化提取的令牌
     */
    analyzeTokens() {
        const result = {
            summary: {
                cookiesFound: Object.keys(this.tokens.cookies).length,
                localStorageFound: Object.keys(this.tokens.localStorage).length,
                sessionStorageFound: Object.keys(this.tokens.sessionStorage).length,
                networkHeadersFound: Object.keys(this.tokens.networkHeaders).length,
                pageVariablesFound: Object.keys(this.tokens.pageVariables).length
            },
            primaryTokens: {},
            allTokens: this.tokens,
            recommendations: []
        };

        // 识别主要令牌
        if (this.tokens.cookies.auth_token) {
            result.primaryTokens.authToken = this.tokens.cookies.auth_token;
            result.recommendations.push('✅ 找到主要认证令牌 (auth_token)');
        }

        if (this.tokens.cookies.ct0) {
            result.primaryTokens.csrfToken = this.tokens.cookies.ct0;
            result.recommendations.push('✅ 找到CSRF令牌 (ct0)');
        }

        if (this.tokens.networkHeaders.Authorization) {
            result.primaryTokens.bearerToken = this.tokens.networkHeaders.Authorization;
            result.recommendations.push('✅ 找到Bearer令牌');
        }

        // 生成使用建议
        if (Object.keys(result.primaryTokens).length === 0) {
            result.recommendations.push('⚠️ 未找到主要认证令牌，请确保已登录Twitter');
        } else {
            result.recommendations.push('🎯 可以使用找到的令牌进行API调用');
        }

        return result;
    }

    /**
     * 导出令牌为JSON格式
     */
    exportTokens() {
        const data = {
            timestamp: new Date().toISOString(),
            url: window.location.href,
            tokens: this.tokens
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `twitter_tokens_${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        console.log('📁 令牌已导出到文件');
    }
}

// 使用方法
console.log('🚀 Twitter AuthToken 提取器已加载');
console.log('📖 使用方法:');
console.log('  const extractor = new TwitterAuthExtractor();');
console.log('  const tokens = await extractor.extractAllTokens();');
console.log('  extractor.exportTokens(); // 导出到文件');

// 自动执行（可选）
// const extractor = new TwitterAuthExtractor();
// extractor.extractAllTokens().then(tokens => {
//     console.log('🎉 自动提取完成！', tokens);
// });
