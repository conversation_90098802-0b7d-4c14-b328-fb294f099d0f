aiohttp-proxy==0.1.2 ; python_version >= "3.11" and python_version < "4.0"
aiohttp-socks==0.8.4 ; python_version >= "3.11" and python_version < "4.0"
aiohttp==3.9.1 ; python_version >= "3.11" and python_version < "4.0"
aioimaplib==1.0.1 ; python_version >= "3.11" and python_version < "4.0"
aiosignal==1.3.1 ; python_version >= "3.11" and python_version < "4.0"
async-lru==2.0.4 ; python_version >= "3.11" and python_version < "4.0"
async-timeout==4.0.3 ; python_version >= "3.11" and python_version < "4.0"
attrs==23.1.0 ; python_version >= "3.11" and python_version < "4.0"
better-automation==1.2.0 ; python_version >= "3.11" and python_version < "4.0"
better-proxy==0.2.1 ; python_version >= "3.11" and python_version < "4.0"
colorama==0.4.6 ; python_version >= "3.11" and python_version < "4.0" and (sys_platform == "win32" or platform_system == "Windows")
frozenlist==1.4.0 ; python_version >= "3.11" and python_version < "4.0"
idna==3.6 ; python_version >= "3.11" and python_version < "4.0"
loguru==0.7.2 ; python_version >= "3.11" and python_version < "4.0"
multidict==6.0.4 ; python_version >= "3.11" and python_version < "4.0"
prompt-toolkit==3.0.36 ; python_version >= "3.11" and python_version < "4.0"
python-socks[asyncio]==2.4.3 ; python_version >= "3.11" and python_version < "4.0"
pyuseragents==1.0.5 ; python_version >= "3.11" and python_version < "4.0"
questionary==2.0.1 ; python_version >= "3.11" and python_version < "4.0"
tqdm==4.66.1 ; python_version >= "3.11" and python_version < "4.0"
wcwidth==0.2.12 ; python_version >= "3.11" and python_version < "4.0"
win32-setctime==1.1.0 ; python_version >= "3.11" and python_version < "4.0" and sys_platform == "win32"
yarl==1.9.3 ; python_version >= "3.11" and python_version < "4.0"
