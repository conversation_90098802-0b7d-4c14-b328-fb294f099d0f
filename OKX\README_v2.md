# OKX 钱包连接脚本 v2.0

## 🎯 基于真实抓包数据的完整钱包连接流程

本脚本基于 **2025-08-05 14:07:00 UTC** 的真实抓包数据，完整模拟 OKX 钱包连接流程。

### ✅ 抓包验证的事件序列

1. **`web_metax_metaxscan_click`** - 用户点击"连接钱包"按钮
2. **`web_metax_metaxextension_click`** - 用户选择 OKX Wallet
3. **`onchain_web_wallet_users`** - 钱包连接成功 (关键事件!)

### 🔧 安装依赖

```bash
npm install ethers node-fetch
# 或者
npm run install-deps
```

### 📝 准备私钥文件

1. 复制示例文件：
```bash
cp private_keys_example.txt private_keys.txt
```

2. 编辑 `private_keys.txt`，添加你的私钥：
```
# 每行一个私钥，支持带或不带 0x 前缀
c5465844a6d55d36b9c8b3b6b5c8e5e5e5e5e5e5e5e5e5e5e5e5e5e5e5e5e5e5
0xd4576855b7d66d47c9c9c4c7c6c9f6f6f6f6f6f6f6f6f6f6f6f6f6f6f6f6f6f6
```

### 🚀 运行脚本

#### 方式1: 批量连接钱包
```bash
npm run connect
# 或者
node okx_wallet_connect_v2.js
```

#### 方式2: 测试单个钱包
```bash
npm test
# 或者
node test_okx_connect.js
```

### 📊 输出结果

脚本会生成详细的连接报告：

```
📊 连接报告:
   总钱包数: 3
   成功连接: 3
   失败连接: 0
   成功率: 100%

📋 详细结果:
1. ✅ 钱包1
   地址: ******************************************
   步骤: 3/3 完成
```

### 📁 生成的文件

- `okx_connect_results_[timestamp].json` - 详细连接结果
- `okx_connect_result_[timestamp].json` - 单次测试结果

### 🔍 真实抓包数据验证

本脚本基于以下真实 API 调用：

```json
{
  "url": "https://web3.okx.com/amplitude/2/httpapi",
  "method": "POST",
  "api_key": "56bf9d43d57f079e506b4f26c70a698f",
  "event_type": "onchain_web_wallet_users",
  "event_properties": {
    "Amount": 1,
    "Type": "Connected",
    "web_mode_okx": "wallet",
    "site": "okx_web3"
  }
}
```

### ⚠️ 注意事项

1. **私钥安全**: 请确保私钥文件安全，不要泄露给他人
2. **测试环境**: 建议先使用测试钱包进行测试
3. **请求频率**: 脚本会自动控制请求频率，避免被限制
4. **网络连接**: 确保能够访问 `https://web3.okx.com`

### 🛠️ 故障排除

#### 依赖安装失败
```bash
# 清除缓存重新安装
npm cache clean --force
npm install
```

#### 私钥格式错误
- 确保私钥是64位十六进制字符串
- 支持带或不带 `0x` 前缀

#### 网络连接问题
- 检查网络连接
- 确认防火墙设置
- 尝试使用代理

### 📈 成功率优化

基于真实抓包数据，本脚本的成功率应该接近 100%，因为：

1. ✅ 使用真实的 API 端点和参数
2. ✅ 完整模拟用户操作序列
3. ✅ 正确的事件时序和属性
4. ✅ 简化的请求头（无需复杂认证）

### 🔄 版本历史

- **v2.0** - 基于真实抓包数据的完整实现
- **v1.0** - 初始版本

### 📞 支持

如果遇到问题，请检查：
1. 依赖是否正确安装
2. 私钥格式是否正确
3. 网络连接是否正常
4. 生成的日志文件中的错误信息
