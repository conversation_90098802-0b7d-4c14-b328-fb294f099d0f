{"name": "okx", "version": "1.0.0", "description": "OKX Web3 钱包连接工具", "main": "real_wallet_connect.js", "type": "module", "scripts": {"start": "node real_wallet_connect.js", "connect": "node okx_wallet_connect_v2.js", "connect-headless": "node okx_wallet_connect_headless.js", "connect-simple": "node okx_wallet_connect_simple_headless.js", "connect-enhanced": "node okx_wallet_connect_enhanced.js", "test": "node test_okx_connect.js", "test-headless": "node test_headless.js", "install-deps": "npm install ethers node-fetch puppeteer user-agents"}, "keywords": ["okx", "web3", "wallet", "ethereum"], "author": "", "license": "ISC", "dependencies": {"ethers": "^6.15.0", "node-fetch": "^3.3.2", "puppeteer": "^24.16.0", "user-agents": "^1.1.0"}}