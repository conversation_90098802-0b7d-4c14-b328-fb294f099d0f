// 推特账号配置示例
// 复制这个文件的内容到 推特tokens自动获取.mjs 的 accounts 数组中

const accounts = [
    {
        account: '<EMAIL>',    // 邮箱、用户名或手机号
        screen_name: 'your_username',       // 推特用户名（@后面的部分）
        password: 'your_password',          // 密码
        _2fa: '',                          // 二次验证码（TOTP，如果启用了）
        acid: ''                           // 邮箱验证码（如果需要）
    },
    {
        account: '<EMAIL>',
        screen_name: 'another_username', 
        password: 'another_password',
        _2fa: '',
        acid: ''
    },
    {
        account: '+**********',             // 也可以使用手机号
        screen_name: 'phone_username',
        password: 'phone_password',
        _2fa: '',
        acid: ''
    }
];

// 配置说明：
// 1. account: 登录时使用的标识符
//    - 可以是邮箱地址
//    - 可以是推特用户名（不带@）
//    - 可以是手机号（带国家代码）
//
// 2. screen_name: 推特用户名
//    - 就是 @后面的用户名
//    - 用于备用验证
//
// 3. password: 账号密码
//    - 推特账号的登录密码
//
// 4. _2fa: 二次验证码（可选）
//    - 如果账号启用了 TOTP 二次验证，需要提供当前的验证码
//    - 6位数字，如：'123456'
//    - 如果没有启用二次验证，留空即可
//
// 5. acid: 邮箱验证码（可选）
//    - 某些情况下推特会要求邮箱验证
//    - 如果收到邮箱验证码，填入这里
//    - 一般情况下留空即可

// 使用步骤：
// 1. 将上面的 accounts 数组复制到 推特tokens自动获取.mjs 文件中
// 2. 填入你的真实推特账号信息
// 3. 运行脚本：node 推特tokens自动获取.mjs
// 4. 等待脚本完成，会生成 twitter_tokens.json 文件

// 注意事项：
// - 确保账号信息正确，密码错误会导致登录失败
// - 如果账号启用了二次验证，需要及时提供验证码
// - 建议先用一个账号测试，确认无误后再批量处理
// - 生成的 tokens 文件包含敏感信息，请妥善保管

export { accounts };
