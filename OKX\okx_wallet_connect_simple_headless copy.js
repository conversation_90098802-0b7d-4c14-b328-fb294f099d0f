#!/usr/bin/env node

/**
 * OKX 钱包连接脚本 - 简化无头浏览器版本
 * 
 * 基于测试验证的可靠实现
 * 使用 Puppeteer 无头浏览器绕过网络限制
 */

import puppeteer from 'puppeteer';
import crypto from 'crypto';
import fs from 'fs';
import { Wallet } from 'ethers';

console.log('🤖 OKX 钱包连接脚本 - 简化无头浏览器版本');
console.log('📅 基于真实抓包数据: 2025-08-05 14:07:00 UTC');

class OKXWalletConnectorSimple {
    constructor() {
        this.baseUrl = 'https://web3.okx.com';
        this.amplitudeApiKey = '56bf9d43d57f079e506b4f26c70a698f';
        this.browser = null;
        this.page = null;
    }

    // 初始化无头浏览器
    async initBrowser() {
        console.log('🚀 启动无头浏览器...');
        
        this.browser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu'
            ]
        });

        this.page = await this.browser.newPage();
        
        // 设置用户代理
        await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
        
        // 设置视口
        await this.page.setViewport({ width: 1280, height: 720 });
        
        // 导航到 OKX 页面
        console.log('🌐 导航到 OKX 页面...');
        await this.page.goto(this.baseUrl, { 
            waitUntil: 'domcontentloaded',
            timeout: 60000 
        });
        
        console.log('✅ 无头浏览器初始化成功');
    }

    // 关闭浏览器
    async closeBrowser() {
        if (this.browser) {
            await this.browser.close();
            console.log('🔒 无头浏览器已关闭');
        }
    }

    // 生成钱包地址
    generateWalletAddress(privateKey) {
        try {
            if (!privateKey.startsWith('0x')) {
                privateKey = '0x' + privateKey;
            }
            const wallet = new Wallet(privateKey);
            return wallet.address;
        } catch (error) {
            console.error('❌ 生成钱包地址失败:', error.message);
            throw error;
        }
    }

    // 在浏览器中发送 Amplitude 事件
    async sendAmplitudeEvent(eventType, eventProperties = {}) {
        const deviceId = crypto.randomUUID();
        const sessionId = Date.now().toString();
        const timestamp = Date.now();
        
        const eventData = {
            api_key: this.amplitudeApiKey,
            events: [{
                user_id: "",
                device_id: deviceId,
                session_id: sessionId,
                time: timestamp,
                app_version: "1.10.51",
                platform: "Web",
                language: "zh_CN",
                ip: "$remote",
                insert_id: crypto.randomUUID(),
                event_type: eventType,
                event_properties: {
                    web_mode_okx: "wallet",
                    site: "okx_web3",
                    ...eventProperties
                },
                event_id: Math.floor(Math.random() * 1000) + 100,
                library: "amplitude-ts/2.11.8",
                user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            }],
            options: {},
            client_upload_time: new Date().toISOString(),
            request_metadata: {
                sdk: { metrics: { histogram: {} } }
            }
        };

        try {
            console.log(`📤 发送事件: ${eventType}`);
            
            // 在浏览器上下文中执行 fetch 请求
            const result = await this.page.evaluate(async (url, data) => {
                try {
                    const response = await fetch(url, {
                        method: 'POST',
                        headers: {
                            'Accept': '*/*',
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(data)
                    });

                    if (response.ok) {
                        const result = await response.json();
                        return { success: true, result, status: response.status };
                    } else {
                        const errorText = await response.text();
                        return { success: false, error: errorText, status: response.status };
                    }
                } catch (error) {
                    return { success: false, error: error.message };
                }
            }, `${this.baseUrl}/amplitude/2/httpapi`, eventData);

            if (result.success) {
                console.log(`✅ 事件发送成功: ${eventType}`);
                console.log(`📊 服务器响应: 已处理 ${result.result.events_ingested} 个事件`);
                console.log(`⏰ 服务器时间: ${new Date(result.result.server_upload_time).toLocaleString()}`);
                return { success: true, result: result.result, deviceId, sessionId };
            } else {
                console.log(`⚠️ 事件发送失败: ${eventType} (状态: ${result.status})`);
                console.log(`📝 错误详情: ${result.error}`);
                return { success: false, status: result.status, error: result.error };
            }
        } catch (error) {
            console.error(`❌ 事件发送错误: ${eventType}`, error);
            return { success: false, error: error.message };
        }
    }

    // 延迟函数
    async delay(ms) {
        console.log(`⏸️ 等待 ${ms/1000} 秒...`);
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 完整的钱包连接流程
    async connectWallet(privateKey, walletName = 'Headless钱包') {
        console.log(`\n🚀 开始钱包连接流程: ${walletName}`);
        console.log('='.repeat(60));
        
        try {
            // 生成钱包地址
            const walletAddress = this.generateWalletAddress(privateKey);
            console.log(`💼 钱包地址: ${walletAddress}`);
            
            const results = {};
            
            // 步骤1: 发送连接按钮点击事件
            console.log('\n📝 步骤1: 模拟点击"连接钱包"按钮...');
            results.step1 = await this.sendAmplitudeEvent('web_metax_metaxscan_click');
            await this.delay(2000);
            
            // 步骤2: 发送钱包选择事件
            console.log('\n📝 步骤2: 模拟选择 OKX Wallet...');
            results.step2 = await this.sendAmplitudeEvent('web_metax_metaxextension_click');
            await this.delay(3000);
            
            // 步骤3: 发送钱包连接成功事件
            console.log('\n📝 步骤3: 模拟钱包连接成功...');
            results.step3 = await this.sendAmplitudeEvent('onchain_web_wallet_users', {
                Amount: 1,
                DeviceId: results.step1.deviceId || crypto.randomUUID(),
                Type: "Connected"
            });
            
            const allSuccess = Object.values(results).every(r => r.success);
            
            console.log('\n🎉 钱包连接流程完成！');
            console.log('='.repeat(60));
            
            return {
                success: allSuccess,
                walletName,
                walletAddress,
                deviceId: results.step1.deviceId,
                sessionId: results.step1.sessionId,
                steps: {
                    connectClick: results.step1.success,
                    walletSelect: results.step2.success,
                    walletConnected: results.step3.success
                },
                stepDetails: results,
                timestamp: new Date().toISOString()
            };
            
        } catch (error) {
            console.error('❌ 钱包连接失败:', error.message);
            return {
                success: false,
                walletName,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }
}

// 从文件读取私钥
function loadPrivateKeys(filename = 'private_keys.txt') {
    try {
        if (!fs.existsSync(filename)) {
            console.log(`⚠️ 私钥文件 ${filename} 不存在`);
            return [];
        }
        
        const content = fs.readFileSync(filename, 'utf8');
        const lines = content.split('\n');
        const privateKeys = [];
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            if (!line || line.startsWith('#')) continue;
            
            const cleanKey = line.replace('0x', '');
            if (/^[a-fA-F0-9]{64}$/.test(cleanKey)) {
                privateKeys.push(line.startsWith('0x') ? line : '0x' + line);
            }
        }
        
        console.log(`📖 读取到 ${privateKeys.length} 个有效私钥`);
        return privateKeys;
        
    } catch (error) {
        console.error(`❌ 读取私钥文件失败: ${error.message}`);
        return [];
    }
}

// 单个钱包连接函数
async function connectSingleWallet(privateKey, walletName = 'Unknown') {
    const connector = new OKXWalletConnectorSimple();
    
    try {
        await connector.initBrowser();
        const result = await connector.connectWallet(privateKey, walletName);
        return result;
    } finally {
        await connector.closeBrowser();
    }
}

// 主函数
async function main() {
    console.log('📝 OKX 钱包连接脚本 - 简化无头浏览器版本');
    console.log('🔗 使用 Puppeteer 绕过网络限制\n');

    // 读取私钥文件
    const privateKeys = loadPrivateKeys('private_keys.txt');
    
    if (privateKeys.length === 0) {
        console.log('🔧 没有找到私钥文件，使用测试私钥进行演示...');
        
        const testPrivateKey = 'c5465844a6d55d36b9c8b3b6b5c8e5e5e5e5e5e5e5e5e5e5e5e5e5e5e5e5e5e5';
        const result = await connectSingleWallet(testPrivateKey, '测试钱包');
        
        console.log('\n📊 连接结果:');
        console.log(JSON.stringify(result, null, 2));
        
        const filename = `okx_connect_result_simple_headless_${Date.now()}.json`;
        fs.writeFileSync(filename, JSON.stringify(result, null, 2));
        console.log(`💾 结果已保存到: ${filename}`);
        
        return;
    }

    console.log(`✅ 成功加载 ${privateKeys.length} 个钱包私钥\n`);
    
    // 批量连接钱包
    const connector = new OKXWalletConnectorSimple();
    
    try {
        await connector.initBrowser();
        
        const results = [];
        for (let i = 0; i < privateKeys.length; i++) {
            const privateKey = privateKeys[i];
            const walletName = `钱包${i + 1}`;
            
            console.log(`\n📋 进度: ${i + 1}/${privateKeys.length} - ${walletName}`);
            
            const result = await connector.connectWallet(privateKey, walletName);
            results.push(result);
            
            // 避免请求过于频繁
            if (i < privateKeys.length - 1) {
                console.log('⏸️ 等待5秒后处理下一个钱包...');
                await connector.delay(5000);
            }
        }
        
        // 保存结果
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `okx_connect_results_simple_headless_${timestamp}.json`;
        fs.writeFileSync(filename, JSON.stringify(results, null, 2));
        console.log(`\n💾 详细结果已保存到: ${filename}`);
        
        // 生成报告
        const successCount = results.filter(r => r.success).length;
        console.log(`\n📊 连接报告:`);
        console.log(`   总钱包数: ${results.length}`);
        console.log(`   成功连接: ${successCount}`);
        console.log(`   失败连接: ${results.length - successCount}`);
        console.log(`   成功率: ${Math.round(successCount / results.length * 100)}%`);
        
        // 显示详细结果
        console.log(`\n📋 详细结果:`);
        results.forEach((result, index) => {
            const status = result.success ? '✅' : '❌';
            console.log(`${index + 1}. ${status} ${result.walletName}`);
            if (result.success) {
                console.log(`   地址: ${result.walletAddress}`);
                const stepCount = Object.values(result.steps).filter(Boolean).length;
                console.log(`   步骤: ${stepCount}/3 完成`);
            } else {
                console.log(`   错误: ${result.error}`);
            }
        });
        
    } finally {
        await connector.closeBrowser();
    }
}

// 直接运行主函数
main().catch(console.error);

export { OKXWalletConnectorSimple, connectSingleWallet };
