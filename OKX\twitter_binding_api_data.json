{"captureInfo": {"timestamp": "2025-08-06T23:15:00Z", "totalRequests": 10, "captureStartTime": 1754522093633, "captureEndTime": 1754522138596, "totalDurationMs": 44963, "tabUrl": "https://web3.okx.com/zh-hans/giveaway/jaspervault", "walletAddress": "******************************************"}, "keyTwitterBindingAPIs": {"oauth2Url": {"url": "https://web3.okx.com/priapi/v1/dapp/oauth2/oauth2-url", "method": "POST", "description": "获取Twitter OAuth2授权URL", "requestBody": {"domain": "web3.okx.com", "walletAddress": "******************************************", "platform": 1, "bizType": 1, "bizRequestData": {"giveawayId": 389, "chainId": 8453}}, "responseBody": {"code": 0, "data": {"clientId": "RVE1ZGwyREQyeGFhaVFwTTFhX0E6MTpjaQ", "url": "https://x.com/i/oauth2/authorize?response_type=code&client_id=RVE1ZGwyREQyeGFhaVFwTTFhX0E6MTpjaQ&redirect_uri=https%3A%2F%2Fweb3.okx.com%2Fmarketplace%2Fdrops%2Fshare%3Fplatform%3Dtwitter&scope=tweet.read+users.read+follows.read+offline.access&state=state&code_challenge=challenge&code_challenge_method=plain"}}}, "callBack": {"url": "https://web3.okx.com/priapi/v1/dapp/oauth2/call-back", "method": "POST", "description": "处理Twitter OAuth2回调，完成绑定", "requestBody": {"code": "aFNWZWc5YXBNSVJMUzU0dUxyQ0tTX01PVkNUUzlRRHVGLWJjRjk1b1NWQlpPOjE3NTQ1MjIxMTUyNTU6MTowOmFjOjE", "clientId": "RVE1ZGwyREQyeGFhaVFwTTFhX0E6MTpjaQ", "walletAddress": "******************************************", "platform": 1, "bizType": 1, "bizRequestData": {"giveawayId": 389, "chainId": 8453}, "domain": "web3.okx.com"}, "responseBody": {"code": 0, "data": {"bindStatus": 1, "bindedWalletAddress": ""}}}}, "authenticationHeaders": {"required": ["Ok-Timestamp", "Ok-Verify-Sign", "Ok-Verify-Token", "X-FpToken-Signature", "X-Id-Group", "X-Request-Timestamp"], "examples": {"Ok-Timestamp": "1754522107077", "Ok-Verify-Sign": "uIJKlWTU6VTvRWs9vfmn5gNZDM4onqaqsT7C5GCSeLI=", "Ok-Verify-Token": "b79777af-4da9-42fc-89f4-e15bbb649793", "X-FpToken-Signature": "{P1363}k1iTPB8d2SxLJDHMjR9gciZcajOG9VNLN0BA9Lii5C0ZrAufhw4DQrS9f7yX6KAn9ixNlvZ8JCzWR4Fr+g2fGg==", "X-Id-Group": "2140445218325030003-c-71", "X-Request-Timestamp": "1754522107076"}}, "commonHeaders": {"Accept": "application/json", "App-Type": "web", "Content-Type": "application/json", "Devid": "3160cbba-52b3-438a-846e-e2bccab5b146", "Referer": "https://web3.okx.com/zh-hans/giveaway/jaspervault", "X-Cdn": "https://web3.okx.com", "X-Locale": "zh_CN", "X-Simulated-Trading": "undefined", "X-Site-Info": "==QfxojI5RXa05WZiwiIMFkQPx0Rfh1SPJiOiUGZvNmIsICUKJiOi42bpdWZyJye", "X-Utc": "8", "X-Zkdex-Env": "0"}, "twitterOAuthFlow": {"step1": {"description": "请求OAuth2授权URL", "endpoint": "/dapp/oauth2/oauth2-url", "parameters": {"domain": "web3.okx.com", "walletAddress": "钱包地址", "platform": 1, "bizType": 1, "bizRequestData": {"giveawayId": "活动ID", "chainId": "链ID"}}}, "step2": {"description": "用户在Twitter完成授权", "redirectUrl": "https://x.com/i/oauth2/authorize", "scope": "tweet.read users.read follows.read offline.access"}, "step3": {"description": "处理授权回调", "endpoint": "/dapp/oauth2/call-back", "parameters": {"code": "Twitter返回的授权码", "clientId": "OAuth2客户端ID", "walletAddress": "钱包地址", "platform": 1, "bizType": 1, "bizRequestData": {"giveawayId": "活动ID", "chainId": "链ID"}, "domain": "web3.okx.com"}}}, "bindingResult": {"bindStatus": 1, "description": "1表示绑定成功，0表示绑定失败", "bindedWalletAddress": "绑定的钱包地址（可能为空）"}}