// 推特账号使用示例
// 演示如何使用生成的账号进行各种操作

import fs from 'fs';
import { 
    createOAuthSignature, 
    makeAuthenticatedRequest, 
    validateAccount, 
    getUserInfo, 
    searchTweets, 
    getUserTimeline 
} from './oauth-utils.mjs';

// 加载生成的账号
function loadAccounts() {
    try {
        const data = JSON.parse(fs.readFileSync('./twitter_accounts.json', 'utf8'));
        return data.accounts || [];
    } catch (error) {
        console.error('加载账号失败:', error.message);
        return [];
    }
}

// 示例1: 验证账号
async function example1_validateAccounts() {
    console.log('\n=== 示例1: 验证账号 ===');
    
    const accounts = loadAccounts();
    if (accounts.length === 0) {
        console.log('没有找到账号，请先运行注册脚本');
        return;
    }
    
    console.log(`开始验证 ${accounts.length} 个账号...`);
    
    let validCount = 0;
    for (let i = 0; i < Math.min(accounts.length, 5); i++) {
        const account = accounts[i];
        console.log(`验证账号 ${i + 1}: ${account.screen_name}`);
        
        const isValid = await validateAccount(account);
        if (isValid) {
            validCount++;
            console.log(`✅ 账号有效`);
        } else {
            console.log(`❌ 账号无效或已过期`);
        }
    }
    
    console.log(`验证完成: ${validCount}/${Math.min(accounts.length, 5)} 个账号有效`);
}

// 示例2: 搜索推文
async function example2_searchTweets() {
    console.log('\n=== 示例2: 搜索推文 ===');
    
    const accounts = loadAccounts();
    if (accounts.length === 0) {
        console.log('没有找到账号');
        return;
    }
    
    const account = accounts[0];
    console.log(`使用账号: ${account.screen_name}`);
    
    try {
        const results = await searchTweets(account, 'JavaScript', {
            count: 5,
            result_type: 'recent'
        });
        
        console.log(`找到 ${results.statuses.length} 条推文:`);
        results.statuses.forEach((tweet, index) => {
            console.log(`${index + 1}. @${tweet.user.screen_name}: ${tweet.text.substring(0, 100)}...`);
        });
        
    } catch (error) {
        console.error('搜索失败:', error.message);
    }
}

// 示例3: 获取用户信息
async function example3_getUserInfo() {
    console.log('\n=== 示例3: 获取用户信息 ===');
    
    const accounts = loadAccounts();
    if (accounts.length === 0) {
        console.log('没有找到账号');
        return;
    }
    
    const account = accounts[0];
    console.log(`使用账号: ${account.screen_name}`);
    
    try {
        // 获取 Twitter 官方账号信息
        const userInfo = await getUserInfo(account, 'twitter');
        
        console.log('用户信息:');
        console.log(`- 用户名: @${userInfo.screen_name}`);
        console.log(`- 显示名: ${userInfo.name}`);
        console.log(`- 粉丝数: ${userInfo.followers_count.toLocaleString()}`);
        console.log(`- 关注数: ${userInfo.friends_count.toLocaleString()}`);
        console.log(`- 推文数: ${userInfo.statuses_count.toLocaleString()}`);
        console.log(`- 简介: ${userInfo.description}`);
        
    } catch (error) {
        console.error('获取用户信息失败:', error.message);
    }
}

// 示例4: 获取用户时间线
async function example4_getUserTimeline() {
    console.log('\n=== 示例4: 获取用户时间线 ===');
    
    const accounts = loadAccounts();
    if (accounts.length === 0) {
        console.log('没有找到账号');
        return;
    }
    
    const account = accounts[0];
    console.log(`使用账号: ${account.screen_name}`);
    
    try {
        // 获取 Twitter 官方账号的时间线
        const timeline = await getUserTimeline(account, 'twitter', {
            count: 5
        });
        
        console.log(`获取到 ${timeline.length} 条推文:`);
        timeline.forEach((tweet, index) => {
            const date = new Date(tweet.created_at).toLocaleString();
            console.log(`${index + 1}. [${date}] ${tweet.text.substring(0, 100)}...`);
        });
        
    } catch (error) {
        console.error('获取时间线失败:', error.message);
    }
}

// 示例5: 使用 GraphQL API
async function example5_graphqlAPI() {
    console.log('\n=== 示例5: 使用 GraphQL API ===');
    
    const accounts = loadAccounts();
    if (accounts.length === 0) {
        console.log('没有找到账号');
        return;
    }
    
    const account = accounts[0];
    console.log(`使用账号: ${account.screen_name}`);
    
    try {
        // 使用 GraphQL API 搜索
        const variables = {
            rawQuery: 'Node.js',
            count: 5,
            querySource: 'typed_query',
            product: 'Top'
        };
        
        const features = {
            longform_notetweets_inline_media_enabled: true,
            super_follow_badge_privacy_enabled: true,
            longform_notetweets_rich_text_read_enabled: true,
            super_follow_user_api_enabled: true,
            android_graphql_skip_api_media_color_palette: true,
            creator_subscriptions_tweet_preview_api_enabled: true,
            freedom_of_speech_not_reach_fetch_enabled: true,
            creator_subscriptions_subscription_count_enabled: true,
            tweetypie_unmention_optimization_enabled: true,
            longform_notetweets_consumption_enabled: true,
            subscriptions_verification_info_enabled: true,
            blue_business_profile_image_shape_enabled: true,
            tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled: true,
            super_follow_exclusive_tweet_notifications_enabled: true
        };
        
        const params = new URLSearchParams({
            variables: JSON.stringify(variables),
            features: JSON.stringify(features)
        });
        
        const url = `https://api.twitter.com/graphql/G8jKRx5LiyrRDs5FcsUjsw/SearchTimeline?${params}`;
        
        const response = await makeAuthenticatedRequest(account, 'GET', url);
        
        if (response.ok) {
            const data = await response.json();
            console.log('GraphQL API 调用成功');
            console.log('返回数据结构:', Object.keys(data));
        } else {
            console.log(`GraphQL API 调用失败: ${response.status} ${response.statusText}`);
        }
        
    } catch (error) {
        console.error('GraphQL API 调用失败:', error.message);
    }
}

// 示例6: 批量操作
async function example6_batchOperations() {
    console.log('\n=== 示例6: 批量操作 ===');
    
    const accounts = loadAccounts();
    if (accounts.length === 0) {
        console.log('没有找到账号');
        return;
    }
    
    console.log(`使用 ${Math.min(accounts.length, 3)} 个账号进行批量搜索...`);
    
    const queries = ['JavaScript', 'Python', 'Node.js'];
    const results = [];
    
    for (let i = 0; i < Math.min(accounts.length, 3); i++) {
        const account = accounts[i];
        const query = queries[i % queries.length];
        
        try {
            console.log(`账号 ${account.screen_name} 搜索: ${query}`);
            const searchResult = await searchTweets(account, query, { count: 3 });
            
            results.push({
                account: account.screen_name,
                query,
                count: searchResult.statuses.length
            });
            
            // 避免请求过快
            await new Promise(resolve => setTimeout(resolve, 1000));
            
        } catch (error) {
            console.error(`账号 ${account.screen_name} 搜索失败:`, error.message);
        }
    }
    
    console.log('\n批量操作结果:');
    results.forEach(result => {
        console.log(`- ${result.account}: "${result.query}" 找到 ${result.count} 条结果`);
    });
}

// 示例7: 账号轮换使用
async function example7_accountRotation() {
    console.log('\n=== 示例7: 账号轮换使用 ===');
    
    const accounts = loadAccounts();
    if (accounts.length === 0) {
        console.log('没有找到账号');
        return;
    }
    
    console.log(`演示账号轮换，共 ${accounts.length} 个账号`);
    
    // 模拟连续请求，轮换使用账号
    for (let i = 0; i < 5; i++) {
        const account = accounts[i % accounts.length];
        
        try {
            console.log(`请求 ${i + 1}: 使用账号 ${account.screen_name}`);
            
            const results = await searchTweets(account, 'tech', { count: 1 });
            console.log(`✅ 成功获取 ${results.statuses.length} 条结果`);
            
        } catch (error) {
            console.log(`❌ 请求失败: ${error.message}`);
        }
        
        // 请求间隔
        await new Promise(resolve => setTimeout(resolve, 500));
    }
}

// 主函数
async function main() {
    console.log('推特账号使用示例');
    console.log('==================');
    
    const accounts = loadAccounts();
    console.log(`加载了 ${accounts.length} 个账号`);
    
    if (accounts.length === 0) {
        console.log('请先运行 推特批量注册.mjs 生成账号');
        return;
    }
    
    try {
        // 运行所有示例
        await example1_validateAccounts();
        await example2_searchTweets();
        await example3_getUserInfo();
        await example4_getUserTimeline();
        await example5_graphqlAPI();
        await example6_batchOperations();
        await example7_accountRotation();
        
        console.log('\n所有示例执行完成！');
        
    } catch (error) {
        console.error('示例执行失败:', error.message);
    }
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}
