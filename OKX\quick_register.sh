#!/bin/bash

# 推特快速注册脚本 (Bash 版本)
# 基于 Twitter Android API 快速生成少量账号

set -e

# 配置
BEARER_TOKEN="Bearer AAAAAAAAAAAAAAAAAAAAAFXzAwAAAAAAMHCxpeSDG1gLNLghVe8d74hl6k4%3DRUMF4xAQLsbeBhTSRrCiQpJtxoGWeyHrDb5te2jpGskWDFW82F"
USER_AGENT="TwitterAndroid/10.21.0-release.0"
OUTPUT_FILE="accounts.txt"
LOG_FILE="register.log"
MAX_ACCOUNTS=10

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "${BLUE}$1${NC}"
}

log_success() {
    log "SUCCESS" "${GREEN}$1${NC}"
}

log_warn() {
    log "WARN" "${YELLOW}$1${NC}"
}

log_error() {
    log "ERROR" "${RED}$1${NC}"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装，请先安装 curl"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_error "jq 未安装，请先安装 jq"
        echo "Ubuntu/Debian: sudo apt-get install jq"
        echo "CentOS/RHEL: sudo yum install jq"
        echo "macOS: brew install jq"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 生成单个账号
generate_account() {
    local attempt=$1
    log_info "尝试生成账号 #${attempt}..."
    
    # 步骤1: 获取 guest token
    log_info "获取 Guest Token..."
    local guest_token
    guest_token=$(curl -s -X POST \
        "https://api.twitter.com/1.1/guest/activate.json" \
        -H "Authorization: $BEARER_TOKEN" \
        -H "User-Agent: $USER_AGENT" \
        --connect-timeout 30 \
        --max-time 60 | jq -r '.guest_token // empty')
    
    if [[ -z "$guest_token" || "$guest_token" == "null" ]]; then
        log_error "获取 Guest Token 失败"
        return 1
    fi
    
    log_success "获取到 Guest Token: ${guest_token:0:20}..."
    
    # 步骤2: 获取 flow token
    log_info "获取 Flow Token..."
    local flow_token
    flow_token=$(curl -s -X POST \
        "https://api.twitter.com/1.1/onboarding/task.json?flow_name=welcome&api_version=1&known_device_token=&sim_country_code=us" \
        -H "Authorization: $BEARER_TOKEN" \
        -H "Content-Type: application/json" \
        -H "User-Agent: $USER_AGENT" \
        -H "X-Guest-Token: $guest_token" \
        -H "X-Twitter-API-Version: 5" \
        -H "X-Twitter-Client: TwitterAndroid" \
        -H "X-Twitter-Client-Version: 10.21.0-release.0" \
        -H "X-Twitter-Active-User: yes" \
        --connect-timeout 30 \
        --max-time 60 \
        -d '{
            "flow_token": null,
            "input_flow_data": {
                "country_code": null,
                "flow_context": {
                    "referrer_context": {
                        "referral_details": "utm_source=google-play&utm_medium=organic",
                        "referrer_url": ""
                    },
                    "start_location": {
                        "location": "splash_screen"
                    }
                },
                "requested_variant": null,
                "target_user_id": 0
            }
        }' | jq -r '.flow_token // empty')
    
    if [[ -z "$flow_token" || "$flow_token" == "null" ]]; then
        log_error "获取 Flow Token 失败"
        return 1
    fi
    
    log_success "获取到 Flow Token: ${flow_token:0:20}..."
    
    # 步骤3: 创建账号
    log_info "创建账号..."
    local account_data
    account_data=$(curl -s -X POST \
        "https://api.twitter.com/1.1/onboarding/task.json" \
        -H "Authorization: $BEARER_TOKEN" \
        -H "Content-Type: application/json" \
        -H "User-Agent: $USER_AGENT" \
        -H "X-Guest-Token: $guest_token" \
        -H "X-Twitter-API-Version: 5" \
        -H "X-Twitter-Client: TwitterAndroid" \
        -H "X-Twitter-Client-Version: 10.21.0-release.0" \
        -H "X-Twitter-Active-User: yes" \
        --connect-timeout 30 \
        --max-time 60 \
        -d "{
            \"flow_token\": \"$flow_token\",
            \"subtask_inputs\": [{
                \"open_link\": {
                    \"link\": \"next_link\"
                },
                \"subtask_id\": \"NextTaskOpenLink\"
            }]
        }")
    
    # 解析账号信息
    local oauth_token oauth_token_secret screen_name user_id
    oauth_token=$(echo "$account_data" | jq -r '.subtasks[]? | select(.subtask_id == "OpenAccount") | .open_account.oauth_token // empty')
    oauth_token_secret=$(echo "$account_data" | jq -r '.subtasks[]? | select(.subtask_id == "OpenAccount") | .open_account.oauth_token_secret // empty')
    screen_name=$(echo "$account_data" | jq -r '.subtasks[]? | select(.subtask_id == "OpenAccount") | .open_account.user.screen_name // empty')
    user_id=$(echo "$account_data" | jq -r '.subtasks[]? | select(.subtask_id == "OpenAccount") | .open_account.user.id_str // empty')
    
    if [[ -n "$oauth_token" && -n "$oauth_token_secret" && -n "$screen_name" ]]; then
        log_success "成功创建账号: @$screen_name ($user_id)"
        
        # 保存账号信息
        echo "$(date '+%Y-%m-%d %H:%M:%S'),@$screen_name,$user_id,$oauth_token,$oauth_token_secret" >> "$OUTPUT_FILE"
        
        return 0
    else
        log_error "账号创建失败或解析失败"
        log_error "响应数据: $account_data"
        return 1
    fi
}

# 主函数
main() {
    echo "================================================"
    echo "           推特快速注册脚本 v1.0"
    echo "================================================"
    echo ""
    
    # 检查依赖
    check_dependencies
    
    # 初始化输出文件
    if [[ ! -f "$OUTPUT_FILE" ]]; then
        echo "timestamp,screen_name,user_id,oauth_token,oauth_token_secret" > "$OUTPUT_FILE"
        log_info "创建输出文件: $OUTPUT_FILE"
    fi
    
    # 统计变量
    local success_count=0
    local failed_count=0
    local start_time=$(date +%s)
    
    log_info "开始生成 $MAX_ACCOUNTS 个账号..."
    
    # 生成账号
    for i in $(seq 1 $MAX_ACCOUNTS); do
        echo ""
        log_info "=== 生成第 $i 个账号 ==="
        
        if generate_account "$i"; then
            ((success_count++))
            log_success "账号 $i 生成成功 ✅"
        else
            ((failed_count++))
            log_error "账号 $i 生成失败 ❌"
        fi
        
        # 显示进度
        echo "进度: $i/$MAX_ACCOUNTS (成功: $success_count, 失败: $failed_count)"
        
        # 请求间隔（避免被限制）
        if [[ $i -lt $MAX_ACCOUNTS ]]; then
            log_info "等待 3 秒..."
            sleep 3
        fi
    done
    
    # 最终统计
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo ""
    echo "================================================"
    echo "                  生成完成"
    echo "================================================"
    echo "总耗时: ${duration}秒"
    echo "成功生成: $success_count 个账号"
    echo "失败次数: $failed_count 次"
    echo "成功率: $(( success_count * 100 / MAX_ACCOUNTS ))%"
    echo "账号保存在: $OUTPUT_FILE"
    echo "日志保存在: $LOG_FILE"
    echo ""
    
    if [[ $success_count -gt 0 ]]; then
        log_success "生成的账号列表:"
        tail -n +2 "$OUTPUT_FILE" | while IFS=',' read -r timestamp screen_name user_id oauth_token oauth_token_secret; do
            echo "  - $screen_name ($user_id)"
        done
    fi
    
    echo ""
    echo "使用说明:"
    echo "1. 账号信息已保存到 $OUTPUT_FILE"
    echo "2. 可以使用 oauth_token 和 oauth_token_secret 进行 API 调用"
    echo "3. 这些是临时账号，有效期约一个月"
    echo "4. 请合理使用，避免频繁请求"
}

# 信号处理
trap 'echo ""; log_warn "脚本被中断"; exit 1' INT TERM

# 运行主函数
main "$@"
